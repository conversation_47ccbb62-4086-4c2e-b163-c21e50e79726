
lemon:
  dataSources:
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      url: *******************************************,*************:6446,*************:6446/hacp_uat_database?autoReconnect=true&serverTimezone=Asia/Shanghai
      username: hacpadm
      password: 'F6mK#w3u'
  security:
    authorize-requests:
      permit-all:
        - /v1/sys/encrypt-password
        - /v1/sys/captcha/getCaptcha
        - /v1/sys/cipher/rsa/publickey
        - /v1/sys/cipher/sm2/publickey
        - /v1/sys/cipher/keys
        - /v1/tenant/sys/log/add
        - /v1/sys/log/add
        - /v1/bui/dynamic/log/add
        - /v1/bui/dynamic/log/update
        - /v1/tenant/workspace/user/exist
        - /v1/tenant/workspace/application/sync
        - /v1/tenant/workspace/application/sync-all
        - /webjars/**
        - /swagger-resources
        - /swagger-resources/**
        - /v2/api-docs
        - /doc.html
        - /v1/message/notice/send-message-email
        - /v1/emergency/container/sub-info
        - /v1/emergency-host/sub/query/list

hacp:
  web:
    dashboard:
      grafana:
        api-url: 'http://*************:3000/api'
        dashboard-url-http: 'http://*************:3000/'
        dashboard-url-https: 'https://*************:3443/'
  emergence:
    kubesphere:
      properties:
        url: 'http://************:31407'
        client-id: 'kubesphere'
        client-secret: 'kubesphere'
        username: 'lihuiquangy'
        password: 'LIhuiquan@2024'
        grant-type: 'password'
        get-token-url: 'http://************:31407/oauth/token'
        get-ns-url: 'http://************:31407/kapis/clusters/<cluster>/tenant.kubesphere.io/v1alpha2/namespaces'
        get-workspaces-url: 'http://************:31407/kapis/tenant.kubesphere.io/v1alpha2/workspaces'
        get-clusters-url: 'http://************:31407/kapis/tenant.kubesphere.io/v1alpha3/workspacetemplates/<workspace>'
        get-ns-pods-url: 'http://************:31407/api/clusters/<cluster>/v1/namespaces/<namespace>/pods'
        delete-pod-url: 'http://************:31407/api/clusters/<cluster>/v1/namespaces/<namespace>/pods/<pod>'
hazelcast:
  cluster-name: hzcluster-hacp-sit

user-ssh-path: ~/.ssh/id_rsa

logging:
  level:
    #debug级别可以打印执行Sql
    org.camunda: debug

