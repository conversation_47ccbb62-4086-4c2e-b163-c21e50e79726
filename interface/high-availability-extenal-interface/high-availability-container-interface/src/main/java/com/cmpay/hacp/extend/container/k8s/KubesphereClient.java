package com.cmpay.hacp.extend.container.k8s;

import com.cmpay.hacp.extend.container.k8s.dto.*;
import feign.FeignException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 后续可能切换成接口调用，不使用rest调用容器接口
 *
 * <AUTHOR>
 * @create 2024/10/12 14:11
 * @since 1.0.0
 */
@FeignClient(url = "${hacp.emergence.kubesphere.properties.url}", name = "kubesphere", contextId = "kubesphereClient")
public interface KubesphereClient {

    @PostMapping(value = "/oauth/token", headers = "Content-Type=application/x-www-form-urlencoded")
    KubesphereTokenRspDTO getToken(@RequestBody KubesphereTokenReqDTO reqDTO);

    @Deprecated
    @GetMapping("/kapis/clusters/{cluster}/tenant.kubesphere.io/v1alpha2/namespaces")
    KubesphereRspDTO getNs(@PathVariable("cluster") String cluster, @RequestHeader("Authorization") String token);

    @Deprecated
    @GetMapping("/kapis/tenant.kubesphere.io/v1alpha2/workspaces")
    KubesphereRspDTO getWorkspaces(@RequestHeader("Authorization") String token);

    @Deprecated
    @GetMapping("/kapis/tenant.kubesphere.io/v1alpha3/workspacetemplates/{workspace}")
    KubesphereClusterRspDTO getClusters(@PathVariable("workspace") String workspace,
            @RequestHeader("Authorization") String token);
    @Deprecated
    @GetMapping("/api/clusters/{cluster}/v1/namespaces/{namespace}/pods")
    KubesphereRspDTO getNsPods(@PathVariable("cluster") String cluster,
            @PathVariable("namespace") String namespace,
            @RequestHeader("Authorization") String token);
    @Deprecated
    @DeleteMapping("/api/clusters/{cluster}/v1/namespaces/{namespace}/pods/{pod}")
    String deletePod(@PathVariable("cluster") String cluster,
            @PathVariable("namespace") String namespace,
            @PathVariable("pod") String pod,
            @RequestHeader("Authorization") String token) throws FeignException;

    @GetMapping("/kapis/clusters/{cluster}/tenant.kubesphere.io/v1beta1/namespaces")
    KubesphereRspDTO getNs_v1beta1(@PathVariable("cluster") String cluster,
            @RequestHeader("Authorization") String token);

    /**
     * 获取与业务空间和业务空间下的所有集群
     * @param token
     * @return
     */
    @GetMapping("/kapis/tenant.kubesphere.io/v1beta1/workspacetemplates")
    KubesphereRspDTO getWorkspaces_v1beta1(@RequestHeader("Authorization") String token);


    @GetMapping("/kapis/tenant.kubesphere.io/v1beta1/workspacetemplates/{workspace}")
    KubesphereClusterRspDTO getClusters_v1beta1(@PathVariable("workspace") String workspace,
            @RequestHeader("Authorization") String token);


    @GetMapping("/clusters/{cluster}/api/v1/namespaces/{namespace}/pods")
    KubesphereRspDTO getNsPods_v1(@PathVariable("cluster") String cluster,
            @PathVariable("namespace") String namespace,
            @RequestHeader("Authorization") String token,
            @RequestParam("labelSelector") String labelSelector);


    @DeleteMapping("/clusters/{cluster}/api/v1/namespaces/{namespace}/pods/{pod}")
    String deletePod_v1(@PathVariable("cluster") String cluster,
            @PathVariable("namespace") String namespace,
            @PathVariable("pod") String pod,
            @RequestHeader("Authorization") String token) throws FeignException;

    @GetMapping("/clusters/{cluster}/apis/apps/v1/namespaces/{namespace}/deployments")
    KubesphereRspDTO getDeploys_v1(@PathVariable("cluster")String cluster,
            @PathVariable("namespace") String namespace,
            @RequestHeader("Authorization")  String accessToken);

    @GetMapping("/clusters/{cluster}/apis/apps/v1/namespaces/{namespace}/deployments/{deployment}")
    KubesphereRspDTO getDeploy_v1(@PathVariable("cluster")String cluster,
            @PathVariable("namespace") String namespace,
            @PathVariable("deployment") String deployment,
            @RequestHeader("Authorization")  String accessToken);

}
