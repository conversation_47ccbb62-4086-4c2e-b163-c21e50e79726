<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IApiLocationDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.ApiLocationDO" >
        <id column="api_location_id" property="apiLocationId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="api_location_cn" property="apiLocationCn" jdbcType="VARCHAR" />
        <result column="api_location_name" property="apiLocationName" jdbcType="VARCHAR" />
        <result column="api_desc" property="apiDesc" jdbcType="VARCHAR" />
        <result column="app_service" property="appService" jdbcType="INTEGER" />
        <result column="app_instance_group" property="appInstanceGroup" jdbcType="INTEGER" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.dispatch.entity.ApiLocationDO" extends="BaseResultMap" >
        <result column="uri_matches" property="uriMatches" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        api_location_id, workspace_id, api_location_cn, api_location_name, api_desc, app_service, 
        app_instance_group, status, operator_id, operator_name, create_time, update_time
    </sql>

    <sql id="Blob_Column_List" >
        uri_matches
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from api_location
        where api_location_id = #{apiLocationId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from api_location
        where api_location_id = #{apiLocationId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationDO" useGeneratedKeys="true" keyProperty="apiLocationId" >
        insert into api_location
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="apiLocationCn != null" >
                api_location_cn,
            </if>
            <if test="apiLocationName != null" >
                api_location_name,
            </if>
            <if test="apiDesc != null" >
                api_desc,
            </if>
            <if test="appService != null" >
                app_service,
            </if>
            <if test="appInstanceGroup != null" >
                app_instance_group,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="uriMatches != null" >
                uri_matches,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="apiLocationCn != null" >
                #{apiLocationCn,jdbcType=VARCHAR},
            </if>
            <if test="apiLocationName != null" >
                #{apiLocationName,jdbcType=VARCHAR},
            </if>
            <if test="apiDesc != null" >
                #{apiDesc,jdbcType=VARCHAR},
            </if>
            <if test="appService != null" >
                #{appService,jdbcType=INTEGER},
            </if>
            <if test="appInstanceGroup != null" >
                #{appInstanceGroup,jdbcType=INTEGER},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="uriMatches != null" >
                #{uriMatches,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationDO" >
        update api_location
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="apiLocationCn != null" >
                api_location_cn = #{apiLocationCn,jdbcType=VARCHAR},
            </if>
            <if test="apiLocationName != null" >
                api_location_name = #{apiLocationName,jdbcType=VARCHAR},
            </if>
            <if test="apiDesc != null" >
                api_desc = #{apiDesc,jdbcType=VARCHAR},
            </if>
            <if test="appService != null" >
                app_service = #{appService,jdbcType=INTEGER},
            </if>
            <if test="appInstanceGroup != null" >
                app_instance_group = #{appInstanceGroup,jdbcType=INTEGER},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="uriMatches != null" >
                uri_matches = #{uriMatches,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where api_location_id = #{apiLocationId,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationDO" >
        update api_location
        set workspace_id = #{workspaceId,jdbcType=VARCHAR},
            api_location_cn = #{apiLocationCn,jdbcType=VARCHAR},
            api_location_name = #{apiLocationName,jdbcType=VARCHAR},
            api_desc = #{apiDesc,jdbcType=VARCHAR},
            app_service = #{appService,jdbcType=INTEGER},
            app_instance_group = #{appInstanceGroup,jdbcType=INTEGER},
            status = #{status,jdbcType=TINYINT},
            operator_id = #{operatorId,jdbcType=VARCHAR},
            operator_name = #{operatorName,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            uri_matches = #{uriMatches,jdbcType=LONGVARCHAR}
        where api_location_id = #{apiLocationId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationDO" >
        select 
        <include refid="Base_Column_List" />
        from api_location
        <where >
            <if test="apiLocationId != null" >
                and api_location_id = #{apiLocationId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="apiLocationCn != null" >
                and api_location_cn = #{apiLocationCn,jdbcType=VARCHAR}
            </if>
            <if test="apiLocationName != null" >
                and api_location_name = #{apiLocationName,jdbcType=VARCHAR}
            </if>
            <if test="apiDesc != null" >
                and api_desc = #{apiDesc,jdbcType=VARCHAR}
            </if>
            <if test="appService != null" >
                and app_service = #{appService,jdbcType=INTEGER}
            </if>
            <if test="appInstanceGroup != null" >
                and app_instance_group = #{appInstanceGroup,jdbcType=INTEGER}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="uriMatches != null" >
                and uri_matches = #{uriMatches,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>