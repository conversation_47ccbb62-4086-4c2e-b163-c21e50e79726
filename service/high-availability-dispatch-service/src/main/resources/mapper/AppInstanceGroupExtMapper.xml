<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IAppInstanceGroupExtDao" >

    <resultMap id="MachineResultMap" type="com.cmpay.hacp.dispatch.bo.AppInstanceMachineBO" >
        <id column="instance_machine_id" property="instanceMachineId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="instance_group_id" property="instanceGroupId" jdbcType="VARCHAR" />
        <result column="instance_group_name" property="instanceGroupName" jdbcType="VARCHAR" />
        <result column="instance_group_cn" property="instanceGroupCn" jdbcType="VARCHAR" />
        <result column="instance_group_desc" property="instanceGroupDesc" jdbcType="VARCHAR" />
        <result column="zone_ids" property="zoneIds" jdbcType="VARCHAR" />
        <result column="addresses" property="addresses" jdbcType="VARCHAR" />
        <result column="weight" property="weight" jdbcType="SMALLINT" />
        <result column="is_backup" property="isBackup" jdbcType="SMALLINT" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Machine_Column_List" >
        aim.instance_group_id,
        aig.instance_group_name,
        aig.instance_group_cn,
        aig.instance_group_desc,
        GROUP_CONCAT(zone_id SEPARATOR ',') AS zone_ids,
        GROUP_CONCAT(address SEPARATOR ',') AS addresses,
        MAX(aig.create_time) AS create_time,
        MAX(aig.update_time) AS update_time,
        MAX(aig.operator_name) AS operator_name,
        MAX(aig.status) AS status
    </sql>

    <select id="getInstanceGroupMachineList" resultMap="MachineResultMap" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO" >
        select
        <include refid="Machine_Column_List" />
        from app_instance_machine aim left join app_instance_group aig on aim.instance_group_id = aig.instance_group_id
        <where >
            <if test="workspaceId != null" >
                and aig.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="instanceGroupId != null" >
                and aig.instance_group_id like concat('%',#{instanceGroupId,jdbcType=VARCHAR},'%')
            </if>
            <if test="address != null" >
                and aim.address like concat('%',#{address,jdbcType=VARCHAR},'%')
            </if>
            <if test="status != null" >
                and aig.status = #{status,jdbcType=TINYINT}
            </if>
            <if test="instanceGroupName != null" >
                and aig.instance_group_name like concat('%',#{instanceGroupName,jdbcType=VARCHAR},'%')
            </if>
            <if test="zoneId != null" >
                and aim.zone_id =  #{zoneId,jdbcType=INTEGER}
            </if>
        </where>
        group by aig.instance_group_id, instance_group_name
    </select>

    <select id="existFind" resultType="com.cmpay.hacp.dispatch.entity.AppInstanceGroupDO" >
        select
        <include refid="com.cmpay.hacp.dispatch.dao.IAppInstanceGroupDao.Base_Column_List" />
        from app_instance_group
        <where>
            workspace_id = #{workspaceId,jdbcType=VARCHAR}
            and instance_group_name = #{instanceGroupName,jdbcType=VARCHAR}
            <if test="instanceGroupId != null " >
                and instance_group_id != #{instanceGroupId,jdbcType=INTEGER}
            </if>
        </where>
    </select>
</mapper>