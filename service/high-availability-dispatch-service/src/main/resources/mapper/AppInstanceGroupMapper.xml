<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IAppInstanceGroupDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.AppInstanceGroupDO" >
        <id column="instance_group_id" property="instanceGroupId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="instance_group_cn" property="instanceGroupCn" jdbcType="VARCHAR" />
        <result column="instance_group_name" property="instanceGroupName" jdbcType="VARCHAR" />
        <result column="instance_group_desc" property="instanceGroupDesc" jdbcType="VARCHAR" />
        <result column="advanced_switch" property="advancedSwitch" jdbcType="TINYINT" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        instance_group_id, workspace_id, instance_group_cn, instance_group_name, instance_group_desc, 
        advanced_switch, status, operator_id, operator_name, create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from app_instance_group
        where instance_group_id = #{instanceGroupId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from app_instance_group
        where instance_group_id = #{instanceGroupId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceGroupDO" useGeneratedKeys="true" keyProperty="instanceGroupId" >
        insert into app_instance_group
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="instanceGroupCn != null" >
                instance_group_cn,
            </if>
            <if test="instanceGroupName != null" >
                instance_group_name,
            </if>
            <if test="instanceGroupDesc != null" >
                instance_group_desc,
            </if>
            <if test="advancedSwitch != null" >
                advanced_switch,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupCn != null" >
                #{instanceGroupCn,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupName != null" >
                #{instanceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupDesc != null" >
                #{instanceGroupDesc,jdbcType=VARCHAR},
            </if>
            <if test="advancedSwitch != null" >
                #{advancedSwitch,jdbcType=TINYINT},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceGroupDO" >
        update app_instance_group
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupCn != null" >
                instance_group_cn = #{instanceGroupCn,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupName != null" >
                instance_group_name = #{instanceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupDesc != null" >
                instance_group_desc = #{instanceGroupDesc,jdbcType=VARCHAR},
            </if>
            <if test="advancedSwitch != null" >
                advanced_switch = #{advancedSwitch,jdbcType=TINYINT},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where instance_group_id = #{instanceGroupId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceGroupDO" >
        select 
        <include refid="Base_Column_List" />
        from app_instance_group
        <where >
            <if test="instanceGroupId != null" >
                and instance_group_id = #{instanceGroupId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="instanceGroupCn != null" >
                and instance_group_cn = #{instanceGroupCn,jdbcType=VARCHAR}
            </if>
            <if test="instanceGroupName != null" >
                and instance_group_name = #{instanceGroupName,jdbcType=VARCHAR}
            </if>
            <if test="instanceGroupDesc != null" >
                and instance_group_desc = #{instanceGroupDesc,jdbcType=VARCHAR}
            </if>
            <if test="advancedSwitch != null" >
                and advanced_switch = #{advancedSwitch,jdbcType=TINYINT}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>