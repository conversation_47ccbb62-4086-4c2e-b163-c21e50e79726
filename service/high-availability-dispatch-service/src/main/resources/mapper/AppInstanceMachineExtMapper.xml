<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IAppInstanceMachineExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.bo.AppInstanceMachineBO" >
        <id column="instance_machine_id" property="instanceMachineId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="instance_group_id" property="instanceGroupId" jdbcType="INTEGER" />
        <result column="zone_id" property="zoneId" jdbcType="BIGINT" />
        <result column="zone_label" property="zoneLabel" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="weight" property="weight" jdbcType="SMALLINT" />
        <result column="is_backup" property="isBackup" jdbcType="SMALLINT" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="findExt" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO" >
        select
        instance_machine_id, a.workspace_id, instance_group_id, zone_id, address, weight, is_backup,
        status, a.operator_id, a.operator_name, a.create_time, a.update_time,zone_label
        from app_instance_machine a left join dispatch_zone z on a.zone_id = z.id
        <where >
            <if test="instanceMachineId != null" >
                and instance_machine_id = #{instanceMachineId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and a.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="instanceGroupId != null" >
                and instance_group_id = #{instanceGroupId,jdbcType=INTEGER}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=VARCHAR}
            </if>
            <if test="address != null" >
                and address = #{address,jdbcType=VARCHAR}
            </if>
            <if test="weight != null" >
                and weight = #{weight,jdbcType=SMALLINT}
            </if>
            <if test="isBackup != null" >
                and is_backup = #{isBackup,jdbcType=SMALLINT}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
        </where>
    </select>
</mapper>