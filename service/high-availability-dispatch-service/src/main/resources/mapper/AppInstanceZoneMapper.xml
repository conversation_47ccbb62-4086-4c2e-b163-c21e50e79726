<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IAppInstanceZoneDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.AppInstanceZoneDO" >
        <id column="instance_zone_id" property="instanceZoneId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="instance_group_id" property="instanceGroupId" jdbcType="INTEGER" />
        <result column="zone_id" property="zoneId" jdbcType="BIGINT" />
        <result column="external_router_address" property="externalRouterAddress" jdbcType="VARCHAR" />
        <result column="check_switch" property="checkSwitch" jdbcType="TINYINT" />
        <result column="check_interval" property="checkInterval" jdbcType="SMALLINT" />
        <result column="check_fall" property="checkFall" jdbcType="SMALLINT" />
        <result column="check_rise" property="checkRise" jdbcType="SMALLINT" />
        <result column="check_timeout" property="checkTimeout" jdbcType="SMALLINT" />
        <result column="check_port" property="checkPort" jdbcType="SMALLINT" />
        <result column="check_http_send" property="checkHttpSend" jdbcType="VARCHAR" />
        <result column="check_http_expect_alive" property="checkHttpExpectAlive" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        instance_zone_id, workspace_id, instance_group_id, zone_id, external_router_address, 
        check_switch, check_interval, check_fall, check_rise, check_timeout, check_port, 
        check_http_send, check_http_expect_alive, status, operator_id, operator_name, create_time, 
        update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from app_instance_zone
        where instance_zone_id = #{instanceZoneId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from app_instance_zone
        where instance_zone_id = #{instanceZoneId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceZoneDO" >
        insert into app_instance_zone
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="instanceZoneId != null" >
                instance_zone_id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="instanceGroupId != null" >
                instance_group_id,
            </if>
            <if test="zoneId != null" >
                zone_id,
            </if>
            <if test="externalRouterAddress != null" >
                external_router_address,
            </if>
            <if test="checkSwitch != null" >
                check_switch,
            </if>
            <if test="checkInterval != null" >
                check_interval,
            </if>
            <if test="checkFall != null" >
                check_fall,
            </if>
            <if test="checkRise != null" >
                check_rise,
            </if>
            <if test="checkTimeout != null" >
                check_timeout,
            </if>
            <if test="checkPort != null" >
                check_port,
            </if>
            <if test="checkHttpSend != null" >
                check_http_send,
            </if>
            <if test="checkHttpExpectAlive != null" >
                check_http_expect_alive,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="instanceZoneId != null" >
                #{instanceZoneId,jdbcType=INTEGER},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupId != null" >
                #{instanceGroupId,jdbcType=INTEGER},
            </if>
            <if test="zoneId != null" >
                #{zoneId,jdbcType=BIGINT},
            </if>
            <if test="externalRouterAddress != null" >
                #{externalRouterAddress,jdbcType=VARCHAR},
            </if>
            <if test="checkSwitch != null" >
                #{checkSwitch,jdbcType=TINYINT},
            </if>
            <if test="checkInterval != null" >
                #{checkInterval,jdbcType=SMALLINT},
            </if>
            <if test="checkFall != null" >
                #{checkFall,jdbcType=SMALLINT},
            </if>
            <if test="checkRise != null" >
                #{checkRise,jdbcType=SMALLINT},
            </if>
            <if test="checkTimeout != null" >
                #{checkTimeout,jdbcType=SMALLINT},
            </if>
            <if test="checkPort != null" >
                #{checkPort,jdbcType=SMALLINT},
            </if>
            <if test="checkHttpSend != null" >
                #{checkHttpSend,jdbcType=VARCHAR},
            </if>
            <if test="checkHttpExpectAlive != null" >
                #{checkHttpExpectAlive,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceZoneDO" >
        update app_instance_zone
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="instanceGroupId != null" >
                instance_group_id = #{instanceGroupId,jdbcType=INTEGER},
            </if>
            <if test="zoneId != null" >
                zone_id = #{zoneId,jdbcType=BIGINT},
            </if>
            <if test="externalRouterAddress != null" >
                external_router_address = #{externalRouterAddress,jdbcType=VARCHAR},
            </if>
            <if test="checkSwitch != null" >
                check_switch = #{checkSwitch,jdbcType=TINYINT},
            </if>
            <if test="checkInterval != null" >
                check_interval = #{checkInterval,jdbcType=SMALLINT},
            </if>
            <if test="checkFall != null" >
                check_fall = #{checkFall,jdbcType=SMALLINT},
            </if>
            <if test="checkRise != null" >
                check_rise = #{checkRise,jdbcType=SMALLINT},
            </if>
            <if test="checkTimeout != null" >
                check_timeout = #{checkTimeout,jdbcType=SMALLINT},
            </if>
            <if test="checkPort != null" >
                check_port = #{checkPort,jdbcType=SMALLINT},
            </if>
            <if test="checkHttpSend != null" >
                check_http_send = #{checkHttpSend,jdbcType=VARCHAR},
            </if>
            <if test="checkHttpExpectAlive != null" >
                check_http_expect_alive = #{checkHttpExpectAlive,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where instance_zone_id = #{instanceZoneId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.AppInstanceZoneDO" >
        select 
        <include refid="Base_Column_List" />
        from app_instance_zone
        <where >
            <if test="instanceZoneId != null" >
                and instance_zone_id = #{instanceZoneId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="instanceGroupId != null" >
                and instance_group_id = #{instanceGroupId,jdbcType=INTEGER}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=BIGINT}
            </if>
            <if test="externalRouterAddress != null" >
                and external_router_address = #{externalRouterAddress,jdbcType=VARCHAR}
            </if>
            <if test="checkSwitch != null" >
                and check_switch = #{checkSwitch,jdbcType=TINYINT}
            </if>
            <if test="checkInterval != null" >
                and check_interval = #{checkInterval,jdbcType=SMALLINT}
            </if>
            <if test="checkFall != null" >
                and check_fall = #{checkFall,jdbcType=SMALLINT}
            </if>
            <if test="checkRise != null" >
                and check_rise = #{checkRise,jdbcType=SMALLINT}
            </if>
            <if test="checkTimeout != null" >
                and check_timeout = #{checkTimeout,jdbcType=SMALLINT}
            </if>
            <if test="checkPort != null" >
                and check_port = #{checkPort,jdbcType=SMALLINT}
            </if>
            <if test="checkHttpSend != null" >
                and check_http_send = #{checkHttpSend,jdbcType=VARCHAR}
            </if>
            <if test="checkHttpExpectAlive != null" >
                and check_http_expect_alive = #{checkHttpExpectAlive,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>