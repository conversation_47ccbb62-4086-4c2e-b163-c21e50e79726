<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchNodeDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchNodeDO" >
        <id column="dispatch_node_id" property="dispatchNodeId" jdbcType="INTEGER" />
        <result column="dispatch_node_name" property="dispatchNodeName" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="zone_id" property="zoneId" jdbcType="BIGINT" />
        <result column="ip_port" property="ipPort" jdbcType="VARCHAR" />
        <result column="gray_dispatch_config_id" property="grayDispatchConfigId" jdbcType="INTEGER" />
        <result column="dispatch_config_id" property="dispatchConfigId" jdbcType="INTEGER" />
        <result column="running_dispatch_config_id" property="runningDispatchConfigId" jdbcType="INTEGER" />
        <result column="gray_dispatch_version" property="grayDispatchVersion" jdbcType="VARCHAR" />
        <result column="dispatch_version" property="dispatchVersion" jdbcType="VARCHAR" />
        <result column="running_dispatch_version" property="runningDispatchVersion" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="push_times" property="pushTimes" jdbcType="TINYINT" />
        <result column="push_time" property="pushTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        dispatch_node_id, dispatch_node_name, workspace_id, zone_id, ip_port, gray_dispatch_config_id, 
        dispatch_config_id, running_dispatch_config_id, gray_dispatch_version, dispatch_version, 
        running_dispatch_version, status, operator_id, operator_name, push_times, push_time, 
        create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_node
        where dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from dispatch_node
        where dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.DispatchNodeDO" useGeneratedKeys="true" keyProperty="dispatchNodeId" >
        insert into dispatch_node
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="dispatchNodeName != null" >
                dispatch_node_name,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="zoneId != null" >
                zone_id,
            </if>
            <if test="ipPort != null" >
                ip_port,
            </if>
            <if test="grayDispatchConfigId != null" >
                gray_dispatch_config_id,
            </if>
            <if test="dispatchConfigId != null" >
                dispatch_config_id,
            </if>
            <if test="runningDispatchConfigId != null" >
                running_dispatch_config_id,
            </if>
            <if test="grayDispatchVersion != null" >
                gray_dispatch_version,
            </if>
            <if test="dispatchVersion != null" >
                dispatch_version,
            </if>
            <if test="runningDispatchVersion != null" >
                running_dispatch_version,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="pushTimes != null" >
                push_times,
            </if>
            <if test="pushTime != null" >
                push_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="dispatchNodeName != null" >
                #{dispatchNodeName,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="zoneId != null" >
                #{zoneId,jdbcType=BIGINT},
            </if>
            <if test="ipPort != null" >
                #{ipPort,jdbcType=VARCHAR},
            </if>
            <if test="grayDispatchConfigId != null" >
                #{grayDispatchConfigId,jdbcType=INTEGER},
            </if>
            <if test="dispatchConfigId != null" >
                #{dispatchConfigId,jdbcType=INTEGER},
            </if>
            <if test="runningDispatchConfigId != null" >
                #{runningDispatchConfigId,jdbcType=INTEGER},
            </if>
            <if test="grayDispatchVersion != null" >
                #{grayDispatchVersion,jdbcType=VARCHAR},
            </if>
            <if test="dispatchVersion != null" >
                #{dispatchVersion,jdbcType=VARCHAR},
            </if>
            <if test="runningDispatchVersion != null" >
                #{runningDispatchVersion,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="pushTimes != null" >
                #{pushTimes,jdbcType=TINYINT},
            </if>
            <if test="pushTime != null" >
                #{pushTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.DispatchNodeDO" >
        update dispatch_node
        <set >
            <if test="dispatchNodeName != null" >
                dispatch_node_name = #{dispatchNodeName,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="zoneId != null" >
                zone_id = #{zoneId,jdbcType=BIGINT},
            </if>
            <if test="ipPort != null" >
                ip_port = #{ipPort,jdbcType=VARCHAR},
            </if>
            <if test="grayDispatchConfigId != null" >
                gray_dispatch_config_id = #{grayDispatchConfigId,jdbcType=INTEGER},
            </if>
            <if test="dispatchConfigId != null" >
                dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER},
            </if>
            <if test="runningDispatchConfigId != null" >
                running_dispatch_config_id = #{runningDispatchConfigId,jdbcType=INTEGER},
            </if>
            <if test="grayDispatchVersion != null" >
                gray_dispatch_version = #{grayDispatchVersion,jdbcType=VARCHAR},
            </if>
            <if test="dispatchVersion != null" >
                dispatch_version = #{dispatchVersion,jdbcType=VARCHAR},
            </if>
            <if test="runningDispatchVersion != null" >
                running_dispatch_version = #{runningDispatchVersion,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="pushTimes != null" >
                push_times = #{pushTimes,jdbcType=TINYINT},
            </if>
            <if test="pushTime != null" >
                push_time = #{pushTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchNodeDO" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_node
        <where >
            <if test="dispatchNodeId != null" >
                and dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER}
            </if>
            <if test="dispatchNodeName != null" >
                and dispatch_node_name = #{dispatchNodeName,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=BIGINT}
            </if>
            <if test="ipPort != null" >
                and ip_port = #{ipPort,jdbcType=VARCHAR}
            </if>
            <if test="grayDispatchConfigId != null" >
                and gray_dispatch_config_id = #{grayDispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="dispatchConfigId != null" >
                and dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="runningDispatchConfigId != null" >
                and running_dispatch_config_id = #{runningDispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="grayDispatchVersion != null" >
                and gray_dispatch_version = #{grayDispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="dispatchVersion != null" >
                and dispatch_version = #{dispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="runningDispatchVersion != null" >
                and running_dispatch_version = #{runningDispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="pushTimes != null" >
                and push_times = #{pushTimes,jdbcType=TINYINT}
            </if>
            <if test="pushTime != null" >
                and push_time = #{pushTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>