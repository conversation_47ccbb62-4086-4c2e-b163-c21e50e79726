<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchPushHistoryExtDao" >

    <resultMap id="HistoryResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO" >
        <id column="push_history_id" property="pushHistoryId" jdbcType="INTEGER" />
        <result column="dispatch_config_id" property="dispatchConfigId" jdbcType="INTEGER" />
        <result column="push_times" property="pushTimes" jdbcType="TINYINT" />
        <result column="dispatch_node_id" property="dispatchNodeId" jdbcType="INTEGER" />
        <result column="dispatch_node_name" property="dispatchNodeName" jdbcType="VARCHAR" />
        <result column="zone_id" property="zoneId" jdbcType="BIGINT" />
        <result column="ip_port" property="ipPort" jdbcType="VARCHAR" />
        <result column="msg_cd" property="msgCd" jdbcType="VARCHAR" />
        <result column="msg_info" property="msgInfo" jdbcType="VARCHAR" />
        <result column="push_start_time" property="pushStartTime" jdbcType="TIMESTAMP" />
        <result column="push_end_time" property="pushEndTime" jdbcType="TIMESTAMP" />
        <result column="config_desc" property="configDesc" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="push_status" property="pushStatus" jdbcType="TINYINT" />
    </resultMap>

    <sql id="History_Column_List" >
        push_history_id, dispatch_config_id, push_times, dispatch_node_id, dispatch_node_name,
        zone_id, ip_port, msg_cd, msg_info, push_start_time, push_end_time, config_desc, operator_id, operator_name,
        push_status
    </sql>

    <sql id="History_Column_Ext_List" >
        push_history_id, dispatch_config_id, push_times, dispatch_node_id, dispatch_node_name,
        zone_id, ip_port, msg_cd, msg_info, push_start_time, push_end_time, config_desc, operator_id, operator_name,
        push_status
    </sql>

    <select id="likeFind" resultMap="HistoryResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO" >
        select
        <include refid="History_Column_List" />
        from dispatch_push_history
        <where >
            <if test="pushHistoryId != null" >
                and push_history_id = #{pushHistoryId,jdbcType=INTEGER}
            </if>
            <if test="dispatchConfigId != null" >
                and dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="pushTimes != null" >
                and push_times = #{pushTimes,jdbcType=TINYINT}
            </if>
            <if test="dispatchNodeId != null" >
                and dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER}
            </if>
            <if test="dispatchNodeName != null" >
                and dispatch_node_name = #{dispatchNodeName,jdbcType=VARCHAR}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=BIGINT}
            </if>
            <if test="ipPort != null" >
                and ip_port = #{ipPort,jdbcType=VARCHAR}
            </if>
            <if test="msgCd != null" >
                msg_cd = #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                msg_info = #{msgInfo,jdbcType=VARCHAR},
            </if>
            <if test="pushStartTime != null" >
                and push_start_time = #{pushStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="pushEndTime != null" >
                and push_end_time = #{pushEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="configDesc != null" >
                and config_desc = #{configDesc,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="pushStatus != null" >
                and push_status = #{pushStatus,jdbcType=TINYINT}
            </if>
            order by push_times desc, push_start_time desc, push_end_time asc
        </where>
    </select>

    <select id="getConfigPublishHistory" resultMap="HistoryResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO" >
        select push_times, config_desc, operator_id, operator_name, push_start_time
        from dispatch_push_history
        <where>
            <if test="dispatchConfigId != null" >
                and dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="pushTimes != null" >
                and push_times = #{pushTimes,jdbcType=INTEGER}
            </if>
        </where>
        group by push_times
        order by push_times desc
    </select>

    <select id="countWorkspaceHistoryPushStatus" resultType="java.lang.Integer" parameterType="com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO" >
        select count(push_history_id)
        from dispatch_push_history
        <where>
            push_status != 1
            <if test="dispatchConfigId != null" >
                and dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="pushTimes != null" >
                and push_times = #{pushTimes,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="skipLockedUnPushedNodeByConfigId" resultMap="HistoryResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO" >
        select
        <include refid="History_Column_List" />
        from dispatch_push_history
        <where >
            <if test="pushHistoryId != null" >
                and push_history_id = #{pushHistoryId,jdbcType=INTEGER}
            </if>
            <if test="dispatchConfigId != null" >
                and dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="pushTimes != null" >
                and push_times = #{pushTimes,jdbcType=TINYINT}
            </if>
            <if test="dispatchNodeId != null" >
                and dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER}
            </if>
            <if test="dispatchNodeName != null" >
                and dispatch_node_name = #{dispatchNodeName,jdbcType=VARCHAR}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=BIGINT}
            </if>
            <if test="ipPort != null" >
                and ip_port = #{ipPort,jdbcType=VARCHAR}
            </if>
            <if test="pushStartTime != null" >
                and push_start_time = #{pushStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="pushEndTime != null" >
                and push_end_time = #{pushEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="configDesc != null" >
                and config_desc = #{configDesc,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="true" >
                and push_status in (0, 2)
            </if>
            order by push_times desc, push_start_time desc, push_end_time asc
        </where>
        for update skip locked
    </select>
</mapper>