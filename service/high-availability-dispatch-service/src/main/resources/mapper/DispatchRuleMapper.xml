<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchRuleDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchRuleDO" >
        <id column="dispatch_rule_id" property="dispatchRuleId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="dispatch_rule_cn" property="dispatchRuleCn" jdbcType="VARCHAR" />
        <result column="dispatch_rule_name" property="dispatchRuleName" jdbcType="VARCHAR" />
        <result column="dispatch_rule_desc" property="dispatchRuleDesc" jdbcType="VARCHAR" />
        <result column="dispatch_rule_type" property="dispatchRuleType" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        dispatch_rule_id, workspace_id, dispatch_rule_cn, dispatch_rule_name, dispatch_rule_desc, 
        dispatch_rule_type, status, operator_id, operator_name, create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_rule
        where dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from dispatch_rule
        where dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.DispatchRuleDO" useGeneratedKeys="true" keyProperty="dispatchRuleId" >
        insert into dispatch_rule
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="dispatchRuleCn != null" >
                dispatch_rule_cn,
            </if>
            <if test="dispatchRuleName != null" >
                dispatch_rule_name,
            </if>
            <if test="dispatchRuleDesc != null" >
                dispatch_rule_desc,
            </if>
            <if test="dispatchRuleType != null" >
                dispatch_rule_type,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleCn != null" >
                #{dispatchRuleCn,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleName != null" >
                #{dispatchRuleName,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleDesc != null" >
                #{dispatchRuleDesc,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleType != null" >
                #{dispatchRuleType,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.DispatchRuleDO" >
        update dispatch_rule
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleCn != null" >
                dispatch_rule_cn = #{dispatchRuleCn,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleName != null" >
                dispatch_rule_name = #{dispatchRuleName,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleDesc != null" >
                dispatch_rule_desc = #{dispatchRuleDesc,jdbcType=VARCHAR},
            </if>
            <if test="dispatchRuleType != null" >
                dispatch_rule_type = #{dispatchRuleType,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchRuleDO" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_rule
        <where >
            <if test="dispatchRuleId != null" >
                and dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchRuleCn != null" >
                and dispatch_rule_cn = #{dispatchRuleCn,jdbcType=VARCHAR}
            </if>
            <if test="dispatchRuleName != null" >
                and dispatch_rule_name = #{dispatchRuleName,jdbcType=VARCHAR}
            </if>
            <if test="dispatchRuleDesc != null" >
                and dispatch_rule_desc = #{dispatchRuleDesc,jdbcType=VARCHAR}
            </if>
            <if test="dispatchRuleType != null" >
                and dispatch_rule_type = #{dispatchRuleType,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>