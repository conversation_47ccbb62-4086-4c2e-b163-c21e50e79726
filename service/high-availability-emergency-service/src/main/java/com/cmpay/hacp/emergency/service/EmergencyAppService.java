package com.cmpay.hacp.emergency.service;

import com.cmpay.hacp.emergency.bo.EmergencyAppBO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/08/23 16:07
 * @since 1.0.0
 */

public interface EmergencyAppService {

    EmergencyAppBO add(EmergencyAppBO bo);

    void update(EmergencyAppBO bo);

    void delete(EmergencyAppBO bo);

    EmergencyAppBO getDetailInfo(EmergencyAppBO bo);

    List<EmergencyAppBO> getList(EmergencyAppBO bo);


    /**
     * key->hostApp
     * value->hostAppId
     * @param bo
     * @return
     */
    Map<String,Integer> getMap(EmergencyAppBO bo);

}
