/*
 * @ClassName IEmergencyHostTagDao
 * @Description 
 * @version 1.0
 * @Date 2024-08-23 14:24:05
 */
package com.cmpay.hacp.emergency.service;

import com.cmpay.hacp.emergency.bo.EmergencyEntityTagBO;
import com.cmpay.hacp.capable.EntityTagCapable;
import com.cmpay.hacp.enums.EntityTypeEnum;

import java.util.List;

public interface EmergencyEntityTagService {

    void add(EmergencyEntityTagBO bo);

    void delete(EmergencyEntityTagBO bo);

    void deleteOrAdd(EntityTagCapable capable, EntityTypeEnum entityType);

    EmergencyEntityTagBO getDetailInfo(EmergencyEntityTagBO bo);

    List<EmergencyEntityTagBO> getList(EmergencyEntityTagBO bo);

}