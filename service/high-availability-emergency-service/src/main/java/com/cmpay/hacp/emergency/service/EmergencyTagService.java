package com.cmpay.hacp.emergency.service;

import com.cmpay.hacp.emergency.bo.EmergencyTagBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/08/23 9:54
 * @since 1.0.0
 */

public interface EmergencyTagService {
    EmergencyTagBO add(EmergencyTagBO bo);

    void update(EmergencyTagBO bo);

    void delete(EmergencyTagBO bo);

    EmergencyTagBO getDetailInfo(EmergencyTagBO bo);

    PageInfo<EmergencyTagBO> getPage(int pageNum, int pageSize, EmergencyTagBO bo);

    List<EmergencyTagBO> getList(EmergencyTagBO bo);

    /**
     * key->hostTag
     * value->hostTagId
     * @param bo
     * @return
     */
    Map<String,Integer> getMap(EmergencyTagBO bo);

    List<EmergencyTagBO> getTagInfoByIds(List<Integer> tagIds);
}
