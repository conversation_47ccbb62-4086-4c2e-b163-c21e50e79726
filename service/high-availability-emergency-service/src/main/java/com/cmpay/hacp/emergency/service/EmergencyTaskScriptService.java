package com.cmpay.hacp.emergency.service;

import com.cmpay.hacp.emergency.bo.EmergencyTaskScriptBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:29
 * @since 1.0.0
 */

public interface EmergencyTaskScriptService {
    void add(EmergencyTaskScriptBO bo);

    void update(EmergencyTaskScriptBO bo);

    void delete(Integer bo);

    EmergencyTaskScriptBO getDetailInfo(Integer id);

    PageInfo<EmergencyTaskScriptBO> getPage(int pageNum, int pageSize, EmergencyTaskScriptBO bo);

    List<EmergencyTaskScriptBO> getList(EmergencyTaskScriptBO bo);
}
