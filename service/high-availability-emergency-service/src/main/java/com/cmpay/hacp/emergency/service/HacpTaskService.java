package com.cmpay.hacp.emergency.service;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.enums.KeyValue;
import com.cmpay.lemon.framework.page.PageInfo;
import org.camunda.bpm.engine.rest.dto.task.TaskDto;

import java.util.List;
import java.util.Map;

/**
 * @description: 任务服务
 * <AUTHOR>
 * @date 2024/5/14 15:18
 * @version 1.0
 */
public interface HacpTaskService {
    /**
     * 添加任务
     * @param taskBO 任务BO
     */
    void addTask(HacpEmergencyTaskBO taskBO, String workspaceId);

    /**
     * 查询任务列表
     * @param taskBO 查询条件
     * @param pageNum  当前页数
     * @param pageSize 每页条数
     * @return 任务信息
     */
    PageInfo<HacpEmergencyTaskBO> queryTaskList(HacpEmergencyTaskBO taskBO, int pageNum, int pageSize);

    /**
     * 获取任务详情
     * @param id 主键
     * @return
     */
    HacpEmergencyTaskBO getTaskInfo(Long id, boolean formatParam, boolean hidePassword);

    /**
     * 删除任务
     * @param id 主键
     */
    void deleteTask(HacpEmergencyTaskBO id);
    /**
     * 修改任务
     * @param taskBO 任务BO
     */
    void updateTask(HacpEmergencyTaskBO taskBO);

    List<TaskDto> queryWaitingTasks(String userId, int firstResult, int maxResults);

    String encryptTaskParam(String newTaskParam, String uuid);

    Map<String, List<KeyValue>> getDropDown();
}
