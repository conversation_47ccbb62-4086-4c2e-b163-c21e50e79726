package com.cmpay.hacp.emergency.service.camunda;

import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.rest.dto.repository.ProcessDefinitionDiagramDto;
import org.camunda.bpm.engine.rest.dto.repository.ProcessDefinitionDto;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.springframework.web.multipart.MultipartFile;

/**
 * @description: 流程部署
 * <AUTHOR>
 * @date 2024/5/14 17:59
 * @version 1.0
 */
public interface ProcessDefinitionService {
    /**
     * 部署定义
     *
     * @param definitionName 定义名称
     * @param bpmnXml        BPMN XML 格式
     * @param tenantId       租户 ID
     * @param res            回复
     * @return {@link Deployment}
     */
    Deployment deployDefinition(String definitionName, String bpmnXml, String tenantId, String res);
    /**
     * 部署流程
     * @param definitionName
     * @param file
     * @return
     */
    Deployment deployDefinition(String definitionName, MultipartFile file, String tenantId);

    BpmnModelInstance fileToBpmnInstance(MultipartFile file, String definitionName);

    BpmnModelInstance getBpmnInstance(String processDefinitionId);

    Deployment deployDefinition(String definitionName, BpmnModelInstance bpmn, String tenantId, String res);

    ProcessDefinitionDiagramDto getDefinitionXmlByDeployId(String deployId);

    ProcessDefinitionDiagramDto getDefinitionXml(String definitionId);

    void deleteDefinition(String deployId);

    ProcessDefinitionDto getProcessDefinition(String deployId);
}
