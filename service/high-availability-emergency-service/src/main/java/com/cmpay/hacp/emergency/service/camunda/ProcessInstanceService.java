package com.cmpay.hacp.emergency.service.camunda;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.emergency.bo.process.*;
import com.cmpay.lemon.framework.page.PageInfo;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.rest.dto.history.HistoricVariableInstanceDto;
import org.camunda.bpm.engine.runtime.ProcessInstance;

import java.util.List;
import java.util.Map;

/**
 * @description: 流程启动
 * <AUTHOR>
 * @date 2024/5/14 17:59
 * @version 1.0
 */
public interface ProcessInstanceService {

    /**
     * 根据流程定义ID 启动流程实例
     * @param startProcessBO
     * @return
     */
    ProcessInstance startProcessInstanceByDeployId(StartProcessBO startProcessBO);

    /**
     * 查询流程实例列表
     * @param queryDto
     * @return
     */
    PageInfo<HistoricProcessInstanceExtDto> queryInstanceList(HistoricProcessInstanceQueryExtDto queryDto);

    /**
     *
     * @return
     */
    HistoricProcessInstanceExtDto getProcessInstanceById(String businessKey);

    ProcessInstanceExtDto getRuntimeProcessInstanceById(String businessKey);

    void setRuntimeVariable(HistoricProcessInstanceExtDto processInstanceExt);

    /**
     * 驳回任务
     * @param processInstanceId
     */
    void rejectTask(String processInstanceId);

    void externallyDeleteProcess(String processInstanceId,String taskId,  String comment);

    void setVariable(String executionId, String variableName, Object value);

    void setVariables(String executionId, Map<String, ?> variables);

    HistoricVariableInstanceDto getVariable(String processInstanceId, String variableName);

    HistoricVariableInstanceDto getVariableByProcessInstanceId(String processInstanceId, String variableName);


    void computeNodeStatus(HistoricProcessInstanceExtDto historicProcessInstanceExtDto);

    List<HacpEmergencyTaskBO> sort(String processDefinitionId, Map<String, HacpEmergencyTaskBO> taskIds);

    void deleteProcessInstance(String businessKey);

    List<org.camunda.bpm.engine.task.Task> queryTaskAssignee();

    TaskExecuteLogPage getTaskLog(String businessKey,String activityNodeId, int index);

    void restartProcessInstance(String businessKey,String workspaceId);

    HistoricTaskInstance getHistoryFinishedTaskById(String processInstanceId, String taskId);

    boolean isAssigneeTask(String processInstanceId, String assignee);

    /**
     * 完成任务
     * @param processInstanceId 流程id
     * @param result pass 通过 reject 不通过
     * @param comment 描述
     */
    String completeTask(String processInstanceId, String result, String comment, Map<String, String> variables,boolean async);

    String retryTask(String businessKey,String activityId,Boolean skip);

}
