package com.cmpay.hacp.emergency.service.factory;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpResponse;
import com.cmpay.hacp.emergency.bo.task.HttpRequestBaseBO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.emergency.service.factory.impl.DefaultInterfaceImpl;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/23 15:29
 * @version 1.0
 */
@Slf4j
public abstract class AbstractInterfaceStrategyFactory {

    public static HashMap<String, Class<? extends AbstractInterfaceStrategyFactory>> map;

    static {
        map = new HashMap<>(1);
        map.put("defaultInterfaceImpl", DefaultInterfaceImpl.class);
    }

    /**
     * 签名
     * @param content 请求报文json串
     * @return 签名以及加密结果
     */
    public abstract String doSign(String content);

    /**
     * 验签
     * @param sign 待验签信息
     * @param cipherTextBody 报文体
     * @return 响应json串
     */
    public abstract String verifySign(String sign, String cipherTextBody);

    public abstract String doCipher(String content);

    public abstract String doDecrypt(String cipher);
    /**
     * @return 响应信息
     */
    public abstract HttpResponse request(HttpRequestBaseBO request);

    public static AbstractInterfaceStrategyFactory newInstance(String signClass) {
        if (JudgeUtils.isBlank(signClass)) {
            return newInstance();
        }
        Class<? extends AbstractInterfaceStrategyFactory> clazz = map.get(signClass);
        if (JudgeUtils.isNull(clazz)) {
            log.error("class is null : {}",clazz);
            BusinessException.throwBusinessException(MsgEnum.TASK_PARAM_IS_ERROR);
        }
        return SpringUtil.getBean(signClass, clazz);
    }

    public static AbstractInterfaceStrategyFactory newInstance() {
        return SpringUtil.getBean("defaultInterfaceImpl", DefaultInterfaceImpl.class);
    }
}
