package com.cmpay.hacp.emergency.service.factory.impl;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.emergency.bo.task.MessageParamBO;
import com.cmpay.hacp.bo.task.TaskParam;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.emergency.service.factory.TaskStrategyType;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.emergency.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/6/4 9:44
 * @version 1.0
 */
@Slf4j
@Service(value = SystemNotifyImpl.BEAN_NAME)
public class SystemNotifyImpl extends AbstractTaskStrategyFactory {

    protected final static String BEAN_NAME = "systemNotifyImpl";
    public SystemNotifyImpl() {
        super(Collections.singletonList(new TaskStrategyType("3", "系统通知", BEAN_NAME)));
    }

    @Override
    public <T extends BaseElement> void bpmnTaskFieldHandle(T node, HacpEmergencyTaskBO taskInfo, BpmnModelInstance modelInstance) {
        if (JudgeUtils.isBlank(taskInfo.getTaskParam()) || !checkTaskParam(taskInfo.getTaskParam())) {
            BusinessException.throwBusinessException(MsgEnum.TASK_OPERATOR_IS_NULL);
        }
        //设置设置服务任务执行调度
        if (node instanceof ServiceTask) {
            ((ServiceTask)node).setCamundaDelegateExpression("${doSendMessage}");
            ((ServiceTask)node).setName(taskInfo.getTaskName());
        }
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getBpmTaskType() {
        return CamundaUtil.CAMUNDA_SERVICE_TASK_ELEMENT;
    }

    @Override
    public String getActivityType() {
        return CamundaUtil.SERVICE_TASK_ELEMENT;
    }

    @Override
    public boolean checkTaskParam(String json) {
        MessageParamBO notifyParamBO = JsonUtil.strToObject(json, MessageParamBO.class);
        if (notifyParamBO == null) {
            return false;
        }
        return !JudgeUtils.isEmpty(notifyParamBO.getMessageUserIds()) && !JudgeUtils.isNull(notifyParamBO.getMessageType());
    }

    @Override
    public TaskParam toTaskParam(String json) {
        return JsonUtil.strToObject(json, MessageParamBO.class);
    }
}
