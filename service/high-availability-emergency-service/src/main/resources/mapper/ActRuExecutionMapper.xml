<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IActRuExecutionDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.tenant.entity.ActRuExecutionDO" >
        <id column="ID_" property="id" jdbcType="VARCHAR" />
        <result column="REV_" property="rev" jdbcType="INTEGER" />
        <result column="ROOT_PROC_INST_ID_" property="rootProcInstId" jdbcType="VARCHAR" />
        <result column="PROC_INST_ID_" property="procInstId" jdbcType="VARCHAR" />
        <result column="BUSINESS_KEY_" property="businessKey" jdbcType="VARCHAR" />
        <result column="PARENT_ID_" property="parentId" jdbcType="VARCHAR" />
        <result column="PROC_DEF_ID_" property="procDefId" jdbcType="VARCHAR" />
        <result column="SUPER_EXEC_" property="superExec" jdbcType="VARCHAR" />
        <result column="SUPER_CASE_EXEC_" property="superCaseExec" jdbcType="VARCHAR" />
        <result column="CASE_INST_ID_" property="caseInstId" jdbcType="VARCHAR" />
        <result column="ACT_ID_" property="actId" jdbcType="VARCHAR" />
        <result column="ACT_INST_ID_" property="actInstId" jdbcType="VARCHAR" />
        <result column="IS_ACTIVE_" property="isActive" jdbcType="TINYINT" />
        <result column="IS_CONCURRENT_" property="isConcurrent" jdbcType="TINYINT" />
        <result column="IS_SCOPE_" property="isScope" jdbcType="TINYINT" />
        <result column="IS_EVENT_SCOPE_" property="isEventScope" jdbcType="TINYINT" />
        <result column="SUSPENSION_STATE_" property="suspensionState" jdbcType="INTEGER" />
        <result column="CACHED_ENT_STATE_" property="cachedEntState" jdbcType="INTEGER" />
        <result column="SEQUENCE_COUNTER_" property="sequenceCounter" jdbcType="BIGINT" />
        <result column="TENANT_ID_" property="tenantId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID_, REV_, ROOT_PROC_INST_ID_, PROC_INST_ID_, BUSINESS_KEY_, PARENT_ID_, PROC_DEF_ID_, 
        SUPER_EXEC_, SUPER_CASE_EXEC_, CASE_INST_ID_, ACT_ID_, ACT_INST_ID_, IS_ACTIVE_, 
        IS_CONCURRENT_, IS_SCOPE_, IS_EVENT_SCOPE_, SUSPENSION_STATE_, CACHED_ENT_STATE_, 
        SEQUENCE_COUNTER_, TENANT_ID_
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from ACT_RU_EXECUTION
        where ID_ = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from ACT_RU_EXECUTION
        where ID_ = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.tenant.entity.ActRuExecutionDO" >
        insert into ACT_RU_EXECUTION
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID_,
            </if>
            <if test="rev != null" >
                REV_,
            </if>
            <if test="rootProcInstId != null" >
                ROOT_PROC_INST_ID_,
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_,
            </if>
            <if test="businessKey != null" >
                BUSINESS_KEY_,
            </if>
            <if test="parentId != null" >
                PARENT_ID_,
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_,
            </if>
            <if test="superExec != null" >
                SUPER_EXEC_,
            </if>
            <if test="superCaseExec != null" >
                SUPER_CASE_EXEC_,
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_,
            </if>
            <if test="actId != null" >
                ACT_ID_,
            </if>
            <if test="actInstId != null" >
                ACT_INST_ID_,
            </if>
            <if test="isActive != null" >
                IS_ACTIVE_,
            </if>
            <if test="isConcurrent != null" >
                IS_CONCURRENT_,
            </if>
            <if test="isScope != null" >
                IS_SCOPE_,
            </if>
            <if test="isEventScope != null" >
                IS_EVENT_SCOPE_,
            </if>
            <if test="suspensionState != null" >
                SUSPENSION_STATE_,
            </if>
            <if test="cachedEntState != null" >
                CACHED_ENT_STATE_,
            </if>
            <if test="sequenceCounter != null" >
                SEQUENCE_COUNTER_,
            </if>
            <if test="tenantId != null" >
                TENANT_ID_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="rev != null" >
                #{rev,jdbcType=INTEGER},
            </if>
            <if test="rootProcInstId != null" >
                #{rootProcInstId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null" >
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null" >
                #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="superExec != null" >
                #{superExec,jdbcType=VARCHAR},
            </if>
            <if test="superCaseExec != null" >
                #{superCaseExec,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="actId != null" >
                #{actId,jdbcType=VARCHAR},
            </if>
            <if test="actInstId != null" >
                #{actInstId,jdbcType=VARCHAR},
            </if>
            <if test="isActive != null" >
                #{isActive,jdbcType=TINYINT},
            </if>
            <if test="isConcurrent != null" >
                #{isConcurrent,jdbcType=TINYINT},
            </if>
            <if test="isScope != null" >
                #{isScope,jdbcType=TINYINT},
            </if>
            <if test="isEventScope != null" >
                #{isEventScope,jdbcType=TINYINT},
            </if>
            <if test="suspensionState != null" >
                #{suspensionState,jdbcType=INTEGER},
            </if>
            <if test="cachedEntState != null" >
                #{cachedEntState,jdbcType=INTEGER},
            </if>
            <if test="sequenceCounter != null" >
                #{sequenceCounter,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.tenant.entity.ActRuExecutionDO" >
        update ACT_RU_EXECUTION
        <set >
            <if test="rev != null" >
                REV_ = #{rev,jdbcType=INTEGER},
            </if>
            <if test="rootProcInstId != null" >
                ROOT_PROC_INST_ID_ = #{rootProcInstId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null" >
                BUSINESS_KEY_ = #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null" >
                PARENT_ID_ = #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="superExec != null" >
                SUPER_EXEC_ = #{superExec,jdbcType=VARCHAR},
            </if>
            <if test="superCaseExec != null" >
                SUPER_CASE_EXEC_ = #{superCaseExec,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="actId != null" >
                ACT_ID_ = #{actId,jdbcType=VARCHAR},
            </if>
            <if test="actInstId != null" >
                ACT_INST_ID_ = #{actInstId,jdbcType=VARCHAR},
            </if>
            <if test="isActive != null" >
                IS_ACTIVE_ = #{isActive,jdbcType=TINYINT},
            </if>
            <if test="isConcurrent != null" >
                IS_CONCURRENT_ = #{isConcurrent,jdbcType=TINYINT},
            </if>
            <if test="isScope != null" >
                IS_SCOPE_ = #{isScope,jdbcType=TINYINT},
            </if>
            <if test="isEventScope != null" >
                IS_EVENT_SCOPE_ = #{isEventScope,jdbcType=TINYINT},
            </if>
            <if test="suspensionState != null" >
                SUSPENSION_STATE_ = #{suspensionState,jdbcType=INTEGER},
            </if>
            <if test="cachedEntState != null" >
                CACHED_ENT_STATE_ = #{cachedEntState,jdbcType=INTEGER},
            </if>
            <if test="sequenceCounter != null" >
                SEQUENCE_COUNTER_ = #{sequenceCounter,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null" >
                TENANT_ID_ = #{tenantId,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_ = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.tenant.entity.ActRuExecutionDO" >
        select 
        <include refid="Base_Column_List" />
        from ACT_RU_EXECUTION
        <where >
            <if test="id != null" >
                and ID_ = #{id,jdbcType=VARCHAR}
            </if>
            <if test="rev != null" >
                and REV_ = #{rev,jdbcType=INTEGER}
            </if>
            <if test="rootProcInstId != null" >
                and ROOT_PROC_INST_ID_ = #{rootProcInstId,jdbcType=VARCHAR}
            </if>
            <if test="procInstId != null" >
                and PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR}
            </if>
            <if test="businessKey != null" >
                and BUSINESS_KEY_ = #{businessKey,jdbcType=VARCHAR}
            </if>
            <if test="parentId != null" >
                and PARENT_ID_ = #{parentId,jdbcType=VARCHAR}
            </if>
            <if test="procDefId != null" >
                and PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR}
            </if>
            <if test="superExec != null" >
                and SUPER_EXEC_ = #{superExec,jdbcType=VARCHAR}
            </if>
            <if test="superCaseExec != null" >
                and SUPER_CASE_EXEC_ = #{superCaseExec,jdbcType=VARCHAR}
            </if>
            <if test="caseInstId != null" >
                and CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR}
            </if>
            <if test="actId != null" >
                and ACT_ID_ = #{actId,jdbcType=VARCHAR}
            </if>
            <if test="actInstId != null" >
                and ACT_INST_ID_ = #{actInstId,jdbcType=VARCHAR}
            </if>
            <if test="isActive != null" >
                and IS_ACTIVE_ = #{isActive,jdbcType=TINYINT}
            </if>
            <if test="isConcurrent != null" >
                and IS_CONCURRENT_ = #{isConcurrent,jdbcType=TINYINT}
            </if>
            <if test="isScope != null" >
                and IS_SCOPE_ = #{isScope,jdbcType=TINYINT}
            </if>
            <if test="isEventScope != null" >
                and IS_EVENT_SCOPE_ = #{isEventScope,jdbcType=TINYINT}
            </if>
            <if test="suspensionState != null" >
                and SUSPENSION_STATE_ = #{suspensionState,jdbcType=INTEGER}
            </if>
            <if test="cachedEntState != null" >
                and CACHED_ENT_STATE_ = #{cachedEntState,jdbcType=INTEGER}
            </if>
            <if test="sequenceCounter != null" >
                and SEQUENCE_COUNTER_ = #{sequenceCounter,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null" >
                and TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>