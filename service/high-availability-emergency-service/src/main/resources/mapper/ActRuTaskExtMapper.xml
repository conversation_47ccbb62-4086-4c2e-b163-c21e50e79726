<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IActRuTaskExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.tenant.entity.ActRuTaskDO" >
        <id column="ID_" property="id" jdbcType="VARCHAR" />
        <result column="REV_" property="rev" jdbcType="INTEGER" />
        <result column="EXECUTION_ID_" property="executionId" jdbcType="VARCHAR" />
        <result column="PROC_INST_ID_" property="procInstId" jdbcType="VARCHAR" />
        <result column="PROC_DEF_ID_" property="procDefId" jdbcType="VARCHAR" />
        <result column="CASE_EXECUTION_ID_" property="caseExecutionId" jdbcType="VARCHAR" />
        <result column="CASE_INST_ID_" property="caseInstId" jdbcType="VARCHAR" />
        <result column="CASE_DEF_ID_" property="caseDefId" jdbcType="VARCHAR" />
        <result column="NAME_" property="name" jdbcType="VARCHAR" />
        <result column="PARENT_TASK_ID_" property="parentTaskId" jdbcType="VARCHAR" />
        <result column="DESCRIPTION_" property="description" jdbcType="VARCHAR" />
        <result column="TASK_DEF_KEY_" property="taskDefKey" jdbcType="VARCHAR" />
        <result column="OWNER_" property="owner" jdbcType="VARCHAR" />
        <result column="ASSIGNEE_" property="assignee" jdbcType="VARCHAR" />
        <result column="DELEGATION_" property="delegation" jdbcType="VARCHAR" />
        <result column="PRIORITY_" property="priority" jdbcType="INTEGER" />
        <result column="CREATE_TIME_" property="createTime" jdbcType="TIMESTAMP" />
        <result column="LAST_UPDATED_" property="lastUpdated" jdbcType="TIMESTAMP" />
        <result column="DUE_DATE_" property="dueDate" jdbcType="TIMESTAMP" />
        <result column="FOLLOW_UP_DATE_" property="followUpDate" jdbcType="TIMESTAMP" />
        <result column="SUSPENSION_STATE_" property="suspensionState" jdbcType="INTEGER" />
        <result column="TENANT_ID_" property="tenantId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID_, REV_, EXECUTION_ID_, PROC_INST_ID_, PROC_DEF_ID_, CASE_EXECUTION_ID_, CASE_INST_ID_, 
        CASE_DEF_ID_, NAME_, PARENT_TASK_ID_, DESCRIPTION_, TASK_DEF_KEY_, OWNER_, ASSIGNEE_, 
        DELEGATION_, PRIORITY_, CREATE_TIME_, LAST_UPDATED_, DUE_DATE_, FOLLOW_UP_DATE_, 
        SUSPENSION_STATE_, TENANT_ID_
    </sql>

    <update id="updateUserTaskOperation">
        update ACT_RU_TASK set ASSIGNEE_ = #{assignee,jdbcType=VARCHAR}
        where
        PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR} and TASK_DEF_KEY_ = #{taskDefKey,jdbcType=VARCHAR}
    </update>


</mapper>