<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IEmergencyHostDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.EmergencyHostDO" >
        <id column="host_id" property="hostId" jdbcType="BIGINT" />
        <result column="host_desc" property="hostDesc" jdbcType="VARCHAR" />
        <result column="host_address" property="hostAddress" jdbcType="VARCHAR" />
        <result column="host_port" property="hostPort" jdbcType="INTEGER" />
        <result column="host_username" property="hostUsername" jdbcType="VARCHAR" />
        <result column="host_password" property="hostPassword" jdbcType="VARCHAR" />
        <result column="secret_key" property="secretKey" jdbcType="VARCHAR" />
        <result column="host_app_id" property="hostAppId" jdbcType="INTEGER" />
        <result column="host_os" property="hostOS" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        host_id, host_desc, host_address, host_port, host_username, host_password, secret_key, 
        host_app_id, host_os, workspace_id, operator_id, operator_name, create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from emergency_host
        where host_id = #{hostId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from emergency_host
        where host_id = #{hostId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.emergency.entity.EmergencyHostDO" useGeneratedKeys="true" keyProperty="hostId" >
        insert into emergency_host
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="hostDesc != null" >
                host_desc,
            </if>
            <if test="hostAddress != null" >
                host_address,
            </if>
            <if test="hostPort != null" >
                host_port,
            </if>
            <if test="hostUsername != null" >
                host_username,
            </if>
            <if test="hostPassword != null" >
                host_password,
            </if>
            <if test="secretKey != null" >
                secret_key,
            </if>
            <if test="hostAppId != null" >
                host_app_id,
            </if>
            <if test="hostOS != null" >
                host_os,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="hostDesc != null" >
                #{hostDesc,jdbcType=VARCHAR},
            </if>
            <if test="hostAddress != null" >
                #{hostAddress,jdbcType=VARCHAR},
            </if>
            <if test="hostPort != null" >
                #{hostPort,jdbcType=INTEGER},
            </if>
            <if test="hostUsername != null" >
                #{hostUsername,jdbcType=VARCHAR},
            </if>
            <if test="hostPassword != null" >
                #{hostPassword,jdbcType=VARCHAR},
            </if>
            <if test="secretKey != null" >
                #{secretKey,jdbcType=VARCHAR},
            </if>
            <if test="hostAppId != null" >
                #{hostAppId,jdbcType=INTEGER},
            </if>
            <if test="hostOS != null" >
                #{hostOS,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.emergency.entity.EmergencyHostDO" >
        update emergency_host
        <set >
            <if test="hostDesc != null" >
                host_desc = #{hostDesc,jdbcType=VARCHAR},
            </if>
            <if test="hostAddress != null" >
                host_address = #{hostAddress,jdbcType=VARCHAR},
            </if>
            <if test="hostPort != null" >
                host_port = #{hostPort,jdbcType=INTEGER},
            </if>
            <if test="hostUsername != null" >
                host_username = #{hostUsername,jdbcType=VARCHAR},
            </if>
            <if test="hostPassword != null" >
                host_password = #{hostPassword,jdbcType=VARCHAR},
            </if>
            <if test="secretKey != null" >
                secret_key = #{secretKey,jdbcType=VARCHAR},
            </if>
            <if test="hostAppId != null" >
                host_app_id = #{hostAppId,jdbcType=INTEGER},
            </if>
            <if test="hostOS != null" >
                host_os = #{hostOS,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where host_id = #{hostId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.emergency.entity.EmergencyHostDO" >
        select 
        <include refid="Base_Column_List" />
        from emergency_host
        <where >
            <if test="hostId != null" >
                and host_id = #{hostId,jdbcType=INTEGER}
            </if>
            <if test="hostDesc != null" >
                and host_desc = #{hostDesc,jdbcType=VARCHAR}
            </if>
            <if test="hostAddress != null" >
                and host_address = #{hostAddress,jdbcType=VARCHAR}
            </if>
            <if test="hostPort != null" >
                and host_port = #{hostPort,jdbcType=INTEGER}
            </if>
            <if test="hostUsername != null" >
                and host_username = #{hostUsername,jdbcType=VARCHAR}
            </if>
            <if test="hostPassword != null" >
                and host_password = #{hostPassword,jdbcType=VARCHAR}
            </if>
            <if test="secretKey != null" >
                and secret_key = #{secretKey,jdbcType=VARCHAR}
            </if>
            <if test="hostAppId != null" >
                and host_app_id = #{hostAppId,jdbcType=INTEGER}
            </if>
            <if test="hostOS != null" >
                and host_os = #{hostOS,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>