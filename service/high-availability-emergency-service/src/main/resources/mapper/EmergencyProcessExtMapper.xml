<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IEmergencyProcessExtDao" >


    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.EmergencyProcessDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="business_key" property="businessKey" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="case_title" property="caseTitle" jdbcType="VARCHAR" />
        <result column="process_def_id" property="processDefId" jdbcType="VARCHAR" />
        <result column="case_deploy_id" property="caseDeployId" jdbcType="VARCHAR" />
        <result column="case_desc" property="caseDesc" jdbcType="VARCHAR" />
        <result column="audit_user_id" property="auditUserId" jdbcType="VARCHAR" />
        <result column="start_user" property="startUser" jdbcType="VARCHAR" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="state" property="state" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="BaseResultExtMap" type="com.cmpay.hacp.emergency.bo.EmergencyProcessBO" extends="BaseResultMap">
    </resultMap>
    <sql id="Base_Column_List" >
        id, business_key, workspace_id, case_title, process_def_id, case_deploy_id, case_desc,
        audit_user_id, start_user, end_time, operator_id, operator_name, create_time, update_time,
        status, state
    </sql>

    <update id="updateState">
        update emergency_process
        set
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            end_time = #{endTime,jdbcType=TIMESTAMP},
            state = #{state,jdbcType=VARCHAR}
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
        and business_key = #{businessKey,jdbcType=VARCHAR}
    </update>
    <update id="updateAuditUserId">
        update emergency_process
        set
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            <if test="auditUserId != null and auditUserId != ''">
                audit_user_id = #{auditUserId,jdbcType=VARCHAR}
            </if>
            <if test="auditUserId == null or auditUserId == ''">
                audit_user_id = null
            </if>
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
          and business_key = #{businessKey,jdbcType=VARCHAR}
    </update>
    <update id="updateExt">
        update emergency_process
        <set >
            <if test="caseTitle != null" >
                case_title = #{caseTitle,jdbcType=VARCHAR},
            </if>
            <if test="processDefId != null" >
                process_def_id = #{processDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseDeployId != null" >
                case_deploy_id = #{caseDeployId,jdbcType=VARCHAR},
            </if>
            <if test="caseDesc != null" >
                case_desc = #{caseDesc,jdbcType=VARCHAR},
            </if>
            <if test="auditUserId != null" >
                audit_user_id = #{auditUserId,jdbcType=VARCHAR},
            </if>
            <if test="startUser != null" >
                start_user = #{startUser,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="state != null" >
                state = #{state,jdbcType=VARCHAR},
            </if>
        </set>
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
        and business_key = #{businessKey,jdbcType=VARCHAR}
    </update>

    <delete id="deleteExt" parameterType="com.cmpay.hacp.emergency.entity.EmergencyProcessDO">
        delete from emergency_process
        where id = #{id,jdbcType=BIGINT}
          and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>

    <select id="getDetailInfo" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.emergency.entity.EmergencyProcessDO">
        select
        <include refid="Base_Column_List" />
        from emergency_process
        where
        workspace_id = #{workspaceId,jdbcType=VARCHAR}
        and status = '0'
        <if test="id !=null and id !='' ">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="businessKey !=null and businessKey !='' ">
            and  business_key = #{businessKey,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="getPage" resultMap="BaseResultExtMap" parameterType="com.cmpay.hacp.emergency.bo.EmergencyProcessBO">
        select
        <include refid="Base_Column_List" />
        from emergency_process
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="businessKey != null" >
                and business_key = #{businessKey,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="caseTitle != null" >
                and case_title = #{caseTitle,jdbcType=VARCHAR}
            </if>
            <if test="processDefId != null" >
                and process_def_id = #{processDefId,jdbcType=VARCHAR}
            </if>
            <if test="caseDeployId != null" >
                and case_deploy_id = #{caseDeployId,jdbcType=VARCHAR}
            </if>
            <if test="caseDesc != null" >
                and case_desc = #{caseDesc,jdbcType=VARCHAR}
            </if>
            <if test="auditUserId != null" >
                and audit_user_id = #{auditUserId,jdbcType=VARCHAR}
            </if>
            <if test="startUser != null" >
                and start_user = #{startUser,jdbcType=VARCHAR}
            </if>
            <if test="logPeriods != null" >
                AND create_time &gt;= #{logPeriods[0]}
                AND create_time &lt;= #{logPeriods[1]}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="state != null" >
                and state = #{state,jdbcType=VARCHAR}
            </if>
            <if test="ids != null and ids.size() > 0" >
                AND id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>

            </if>
        </where>
        order by create_time desc
    </select>

</mapper>