<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IHacpEmergencyCaseExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.HacpEmergencyCaseDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="case_name" property="caseName" jdbcType="VARCHAR" />
        <result column="case_describe" property="caseDescribe" jdbcType="VARCHAR" />
        <result column="case_deploy_id" property="caseDeployId" jdbcType="VARCHAR" />
        <result column="operator" property="operator" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="BaseResultExtMap" extends="BaseResultMap" type="com.cmpay.hacp.emergency.bo.HacpEmergencyCaseBO"  >
        <result column="tag_name" property="tagName" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, tenant_id, hacp_emergency_case.workspace_id, case_name, case_describe, case_deploy_id, hacp_emergency_case.operator,
        hacp_emergency_case.operator_name, hacp_emergency_case.create_time, hacp_emergency_case.update_time, hacp_emergency_case.status
    </sql>

    <select id="findExt" resultMap="BaseResultExtMap" parameterType="com.cmpay.hacp.emergency.bo.HacpEmergencyCaseBO" >
        select
        <include refid="Base_Column_List" />
        ,GROUP_CONCAT(tag_name SEPARATOR ',') AS tag_name
        from hacp_emergency_case left join
        (select * from emergency_entity_tag where
             entity_type = #{entityType,jdbcType=VARCHAR}
        )
        ht on hacp_emergency_case.id = ht.entity_id
        left join emergency_tag t on t.tag_id = ht.tag_id
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null" >
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="tagId != null">
                and ht.tag_id = #{tagId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and hacp_emergency_case.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="caseName != null" >
                and case_name like concat('%',#{caseName,jdbcType=VARCHAR},'%')
            </if>
            <if test="caseDescribe != null" >
                and case_describe = #{caseDescribe,jdbcType=VARCHAR}
            </if>
            <if test="caseDeployId != null" >
                and case_deploy_id = #{caseDeployId,jdbcType=VARCHAR}
            </if>
            <if test="operator != null" >
                and hacp_emergency_case.operator = #{operator,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and hacp_emergency_case.operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and hacp_emergency_case.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and hacp_emergency_case.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null" >
                and hacp_emergency_case.status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
        group by hacp_emergency_case.id
        order by hacp_emergency_case.update_time desc
    </select>
</mapper>