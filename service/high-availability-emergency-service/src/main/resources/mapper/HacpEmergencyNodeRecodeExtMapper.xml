<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IHacpEmergencyNodeRecodeExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.HacpEmergencyNodeRecodeDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="case_id" property="caseId" jdbcType="BIGINT" />
        <result column="task_id" property="taskId" jdbcType="BIGINT" />
        <result column="task_name" property="taskName" jdbcType="VARCHAR" />
        <result column="task_describe" property="taskDescribe" jdbcType="VARCHAR" />
        <result column="process_id" property="processId" jdbcType="VARCHAR" />
        <result column="business_key" property="businessKey" jdbcType="VARCHAR" />
        <result column="task_type" property="taskType" jdbcType="VARCHAR" />
        <result column="activity_node_id" property="activityNodeId" jdbcType="VARCHAR" />
        <result column="execute_result" property="executeResult" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="duration" property="duration" jdbcType="INTEGER" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.emergency.entity.HacpEmergencyNodeRecodeDO" extends="BaseResultMap" >
        <result column="task_param" property="taskParam" jdbcType="LONGVARCHAR" />
        <result column="result_log" property="resultLog" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, tenant_id, workspace_id, case_id, task_id, task_name, task_describe, process_id,
        business_key, task_type, activity_node_id, execute_result, create_time, duration,
        tm_smp, end_time
    </sql>

    <sql id="Blob_Column_List_PARAM" >
        task_param
    </sql>

    <sql id="Blob_Column_List" >
        task_param, result_log
    </sql>

    <delete id="deleteExt" parameterType="com.cmpay.hacp.emergency.entity.HacpEmergencyNodeRecodeDO">
        delete from hacp_emergency_node_recode
        where
            business_key = #{businessKey,jdbcType=VARCHAR}
          and activity_node_id = #{activityNodeId,jdbcType=VARCHAR}
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>

    <select id="getLastExecuteRecode" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        from hacp_emergency_node_recode
        where  business_key = #{businessKey,jdbcType=VARCHAR}
        and activity_node_id = #{activityNodeId,jdbcType=VARCHAR}
        order by id desc LIMIT 1
    </select>

    <select id="getLastExecuteRecodeAndLog" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
               ,
        <include refid="Blob_Column_List" />
        from hacp_emergency_node_recode
        where  business_key = #{businessKey,jdbcType=VARCHAR}
        and activity_node_id = #{activityNodeId,jdbcType=VARCHAR}
        order by id desc LIMIT 1
    </select>

    <select id="getExecuteLastRecode" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        from hacp_emergency_node_recode
        where  business_key = #{businessKey,jdbcType=VARCHAR}
        order by id desc LIMIT 1
    </select>

    <select id="getAllRecode" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        from hacp_emergency_node_recode
        where  business_key = #{businessKey,jdbcType=VARCHAR}
    </select>

    <select id="getAllRecodeParam" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
               ,
        <include refid="Blob_Column_List_PARAM" />
        from hacp_emergency_node_recode
        where  business_key = #{businessKey,jdbcType=VARCHAR}
    </select>

    <select id="getLastExecuteLog" resultType="java.lang.String">
        select result_log from hacp_emergency_node_recode
        where  activity_node_id = #{activityNodeId,jdbcType=VARCHAR}
        and business_key = #{businessKey, jdbcType=VARCHAR}
        order by id desc LIMIT 1
    </select>
    <select id="findResult" resultMap="BaseResultMap">
        select execute_result, activity_node_id
        from hacp_emergency_node_recode
        where business_key = #{businessKey,jdbcType=VARCHAR}
    </select>

    <select id="getCompleteRecode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from hacp_emergency_node_recode
        where business_key = #{businessKey,jdbcType=VARCHAR}
        and execute_result in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateSkipNode">
        update hacp_emergency_node_recode
        set execute_result = 3
        where
          activity_node_id = #{activityNodeId,jdbcType=VARCHAR}
          and business_key = #{businessKey, jdbcType=VARCHAR}
    </update>

    <update id="updateProcessInstanceId">
        update hacp_emergency_node_recode
        set process_id =  #{newVal,jdbcType=VARCHAR}
        where  process_id = #{oldVal,jdbcType=VARCHAR}
    </update>

    <update id="updateNodeStatus">
        update hacp_emergency_node_recode
        set execute_result =  #{status,jdbcType=VARCHAR}
        <if test="resultLog !=null">
            and result_log = #{resultLog,jdbcType=VARCHAR}
        </if>
        where
            activity_node_id = #{activityNodeId,jdbcType=VARCHAR}
          and business_key = #{businessKey, jdbcType=VARCHAR}
    </update>
</mapper>