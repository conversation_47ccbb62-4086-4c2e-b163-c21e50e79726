<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IHacpEmergencyNodeRecodeDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.HacpEmergencyNodeRecodeDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="case_id" property="caseId" jdbcType="BIGINT" />
        <result column="task_id" property="taskId" jdbcType="BIGINT" />
        <result column="task_name" property="taskName" jdbcType="VARCHAR" />
        <result column="task_describe" property="taskDescribe" jdbcType="VARCHAR" />
        <result column="process_id" property="processId" jdbcType="VARCHAR" />
        <result column="business_key" property="businessKey" jdbcType="VARCHAR" />
        <result column="task_type" property="taskType" jdbcType="VARCHAR" />
        <result column="activity_node_id" property="activityNodeId" jdbcType="VARCHAR" />
        <result column="execute_result" property="executeResult" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="duration" property="duration" jdbcType="INTEGER" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.emergency.entity.HacpEmergencyNodeRecodeDO" extends="BaseResultMap" >
        <result column="task_param" property="taskParam" jdbcType="LONGVARCHAR" />
        <result column="result_log" property="resultLog" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, tenant_id, workspace_id, case_id, task_id, task_name, task_describe, process_id, 
        business_key, task_type, activity_node_id, execute_result, create_time, duration, 
        tm_smp, end_time
    </sql>

    <sql id="Blob_Column_List" >
        task_param, result_log
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from hacp_emergency_node_recode
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long" >
        delete from hacp_emergency_node_recode
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.emergency.entity.HacpEmergencyNodeRecodeDO" useGeneratedKeys="true" keyProperty="id" >
        insert into hacp_emergency_node_recode
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="tenantId != null" >
                tenant_id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="caseId != null" >
                case_id,
            </if>
            <if test="taskId != null" >
                task_id,
            </if>
            <if test="taskName != null" >
                task_name,
            </if>
            <if test="taskDescribe != null" >
                task_describe,
            </if>
            <if test="processId != null" >
                process_id,
            </if>
            <if test="businessKey != null" >
                business_key,
            </if>
            <if test="taskType != null" >
                task_type,
            </if>
            <if test="activityNodeId != null" >
                activity_node_id,
            </if>
            <if test="executeResult != null" >
                execute_result,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="duration != null" >
                duration,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
            <if test="endTime != null" >
                end_time,
            </if>
            <if test="taskParam != null" >
                task_param,
            </if>
            <if test="resultLog != null" >
                result_log,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="caseId != null" >
                #{caseId,jdbcType=BIGINT},
            </if>
            <if test="taskId != null" >
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="taskName != null" >
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="taskDescribe != null" >
                #{taskDescribe,jdbcType=VARCHAR},
            </if>
            <if test="processId != null" >
                #{processId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null" >
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="taskType != null" >
                #{taskType,jdbcType=VARCHAR},
            </if>
            <if test="activityNodeId != null" >
                #{activityNodeId,jdbcType=VARCHAR},
            </if>
            <if test="executeResult != null" >
                #{executeResult,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null" >
                #{duration,jdbcType=INTEGER},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taskParam != null" >
                #{taskParam,jdbcType=LONGVARCHAR},
            </if>
            <if test="resultLog != null" >
                #{resultLog,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.emergency.entity.HacpEmergencyNodeRecodeDO" >
        update hacp_emergency_node_recode
        <set >
            <if test="tenantId != null" >
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="caseId != null" >
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="taskId != null" >
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="taskName != null" >
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="taskDescribe != null" >
                task_describe = #{taskDescribe,jdbcType=VARCHAR},
            </if>
            <if test="processId != null" >
                process_id = #{processId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null" >
                business_key = #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="taskType != null" >
                task_type = #{taskType,jdbcType=VARCHAR},
            </if>
            <if test="activityNodeId != null" >
                activity_node_id = #{activityNodeId,jdbcType=VARCHAR},
            </if>
            <if test="executeResult != null" >
                execute_result = #{executeResult,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null" >
                duration = #{duration,jdbcType=INTEGER},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taskParam != null" >
                task_param = #{taskParam,jdbcType=LONGVARCHAR},
            </if>
            <if test="resultLog != null" >
                result_log = #{resultLog,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.emergency.entity.HacpEmergencyNodeRecodeDO" >
        update hacp_emergency_node_recode
        set tenant_id = #{tenantId,jdbcType=VARCHAR},
            workspace_id = #{workspaceId,jdbcType=VARCHAR},
            case_id = #{caseId,jdbcType=BIGINT},
            task_id = #{taskId,jdbcType=BIGINT},
            task_name = #{taskName,jdbcType=VARCHAR},
            task_describe = #{taskDescribe,jdbcType=VARCHAR},
            process_id = #{processId,jdbcType=VARCHAR},
            business_key = #{businessKey,jdbcType=VARCHAR},
            task_type = #{taskType,jdbcType=VARCHAR},
            activity_node_id = #{activityNodeId,jdbcType=VARCHAR},
            execute_result = #{executeResult,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            duration = #{duration,jdbcType=INTEGER},
            tm_smp = #{tmSmp,jdbcType=VARCHAR},
            end_time = #{endTime,jdbcType=TIMESTAMP},
            task_param = #{taskParam,jdbcType=LONGVARCHAR},
            result_log = #{resultLog,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.emergency.entity.HacpEmergencyNodeRecodeDO" >
        select 
        <include refid="Base_Column_List" />
        from hacp_emergency_node_recode
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null" >
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="caseId != null" >
                and case_id = #{caseId,jdbcType=BIGINT}
            </if>
            <if test="taskId != null" >
                and task_id = #{taskId,jdbcType=BIGINT}
            </if>
            <if test="taskName != null" >
                and task_name = #{taskName,jdbcType=VARCHAR}
            </if>
            <if test="taskDescribe != null" >
                and task_describe = #{taskDescribe,jdbcType=VARCHAR}
            </if>
            <if test="processId != null" >
                and process_id = #{processId,jdbcType=VARCHAR}
            </if>
            <if test="businessKey != null" >
                and business_key = #{businessKey,jdbcType=VARCHAR}
            </if>
            <if test="taskType != null" >
                and task_type = #{taskType,jdbcType=VARCHAR}
            </if>
            <if test="activityNodeId != null" >
                and activity_node_id = #{activityNodeId,jdbcType=VARCHAR}
            </if>
            <if test="executeResult != null" >
                and execute_result = #{executeResult,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="duration != null" >
                and duration = #{duration,jdbcType=INTEGER}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
            <if test="endTime != null" >
                and end_time = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="taskParam != null" >
                and task_param = #{taskParam,jdbcType=LONGVARCHAR}
            </if>
            <if test="resultLog != null" >
                and result_log = #{resultLog,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>