<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IImportHistoryFileInfoDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.ImportHistoryFileInfoDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="file_name" property="fileName" jdbcType="VARCHAR" />
        <result column="excel_count" property="excelCount" jdbcType="INTEGER" />
        <result column="excel_success" property="excelSuccess" jdbcType="INTEGER" />
        <result column="excel_fail" property="excelFail" jdbcType="INTEGER" />
        <result column="excel_success_remark" property="excelSuccessRemark" jdbcType="VARCHAR" />
        <result column="excel_fail_remark" property="excelFailRemark" jdbcType="VARCHAR" />
        <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.emergency.entity.ImportHistoryFileInfoDO" extends="BaseResultMap" >
        <result column="file_blob" property="fileBlob" jdbcType="LONGVARBINARY" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, file_name, excel_count, excel_success, excel_fail, excel_success_remark, excel_fail_remark, 
        batch_number, create_time, workspace_id, operator_id, operator_name
    </sql>

    <sql id="Blob_Column_List" >
        file_blob
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from import_history_file_info
        where id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from import_history_file_info
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.emergency.entity.ImportHistoryFileInfoDO" >
        insert into import_history_file_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="fileName != null" >
                file_name,
            </if>
            <if test="excelCount != null" >
                excel_count,
            </if>
            <if test="excelSuccess != null" >
                excel_success,
            </if>
            <if test="excelFail != null" >
                excel_fail,
            </if>
            <if test="excelSuccessRemark != null" >
                excel_success_remark,
            </if>
            <if test="excelFailRemark != null" >
                excel_fail_remark,
            </if>
            <if test="batchNumber != null" >
                batch_number,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="fileBlob != null" >
                file_blob,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="fileName != null" >
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="excelCount != null" >
                #{excelCount,jdbcType=INTEGER},
            </if>
            <if test="excelSuccess != null" >
                #{excelSuccess,jdbcType=INTEGER},
            </if>
            <if test="excelFail != null" >
                #{excelFail,jdbcType=INTEGER},
            </if>
            <if test="excelSuccessRemark != null" >
                #{excelSuccessRemark,jdbcType=VARCHAR},
            </if>
            <if test="excelFailRemark != null" >
                #{excelFailRemark,jdbcType=VARCHAR},
            </if>
            <if test="batchNumber != null" >
                #{batchNumber,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="fileBlob != null" >
                #{fileBlob,jdbcType=LONGVARBINARY},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.emergency.entity.ImportHistoryFileInfoDO" >
        update import_history_file_info
        <set >
            <if test="fileName != null" >
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="excelCount != null" >
                excel_count = #{excelCount,jdbcType=INTEGER},
            </if>
            <if test="excelSuccess != null" >
                excel_success = #{excelSuccess,jdbcType=INTEGER},
            </if>
            <if test="excelFail != null" >
                excel_fail = #{excelFail,jdbcType=INTEGER},
            </if>
            <if test="excelSuccessRemark != null" >
                excel_success_remark = #{excelSuccessRemark,jdbcType=VARCHAR},
            </if>
            <if test="excelFailRemark != null" >
                excel_fail_remark = #{excelFailRemark,jdbcType=VARCHAR},
            </if>
            <if test="batchNumber != null" >
                batch_number = #{batchNumber,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="fileBlob != null" >
                file_blob = #{fileBlob,jdbcType=LONGVARBINARY},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.emergency.entity.ImportHistoryFileInfoDO" >
        update import_history_file_info
        set file_name = #{fileName,jdbcType=VARCHAR},
            excel_count = #{excelCount,jdbcType=INTEGER},
            excel_success = #{excelSuccess,jdbcType=INTEGER},
            excel_fail = #{excelFail,jdbcType=INTEGER},
            excel_success_remark = #{excelSuccessRemark,jdbcType=VARCHAR},
            excel_fail_remark = #{excelFailRemark,jdbcType=VARCHAR},
            batch_number = #{batchNumber,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            workspace_id = #{workspaceId,jdbcType=VARCHAR},
            operator_id = #{operatorId,jdbcType=VARCHAR},
            operator_name = #{operatorName,jdbcType=VARCHAR},
            file_blob = #{fileBlob,jdbcType=LONGVARBINARY}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.emergency.entity.ImportHistoryFileInfoDO" >
        select 
        <include refid="Base_Column_List" />
        from import_history_file_info
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="fileName != null" >
                and file_name = #{fileName,jdbcType=VARCHAR}
            </if>
            <if test="excelCount != null" >
                and excel_count = #{excelCount,jdbcType=INTEGER}
            </if>
            <if test="excelSuccess != null" >
                and excel_success = #{excelSuccess,jdbcType=INTEGER}
            </if>
            <if test="excelFail != null" >
                and excel_fail = #{excelFail,jdbcType=INTEGER}
            </if>
            <if test="excelSuccessRemark != null" >
                and excel_success_remark = #{excelSuccessRemark,jdbcType=VARCHAR}
            </if>
            <if test="excelFailRemark != null" >
                and excel_fail_remark = #{excelFailRemark,jdbcType=VARCHAR}
            </if>
            <if test="batchNumber != null" >
                and batch_number = #{batchNumber,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="fileBlob != null" >
                and file_blob = #{fileBlob,jdbcType=LONGVARBINARY}
            </if>
        </where>
    </select>
</mapper>