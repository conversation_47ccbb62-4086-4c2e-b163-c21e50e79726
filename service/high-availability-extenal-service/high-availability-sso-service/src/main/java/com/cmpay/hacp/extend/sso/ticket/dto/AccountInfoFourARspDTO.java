package com.cmpay.hacp.extend.sso.ticket.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 用户同步接口返回对象
 */
@Data
public class AccountInfoFourARspDTO {

    @ApiModelProperty(value = "0=查询成功，其他值=失败")
    private String resultCode;

    @ApiModelProperty(value = "错误描述，当resultCode不为0时有值")
    private String resultMsg;

    @ApiModelProperty(value = "详细信息")
    private List<AccountInfoFourADTO> accounts;

}
