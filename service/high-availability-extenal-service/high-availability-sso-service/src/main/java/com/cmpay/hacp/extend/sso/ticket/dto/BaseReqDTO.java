package com.cmpay.hacp.extend.sso.ticket.dto;

import com.cmpay.lemon.framework.desensitization.Desensitization;
import com.cmpay.lemon.framework.desensitization.Type;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * @author: xujuwnei
 * @Date: 2020/02/15
 * 基础请求DTO
 */
@Data
public class BaseReqDTO implements Serializable {


    @ApiModelProperty(value = "设备ID")
    private String equipmentId;

    @ApiModelProperty(value = "请求IP")
    private String requestIp;

    @ApiModelProperty(value = "访问Token")
    @NotEmpty(message = "UPM00009")
    @Desensitization(Type.MIDDLE)
    private String accessToken;

    @ApiModelProperty(value = "应用ID")
    @NotEmpty(message = "UPM00009")
    @Desensitization(Type.MIDDLE)
    @Pattern(regexp = "^[^`~!@#$%^&*+=|{}':;',//[//].<>/?~！@#￥%……&*——+|{}【】‘；：”“’。，、？]*$", message = "UPM00076")
    @Length(max = 32, message = "UPM00077")
    private String appId;

    @ApiModelProperty(value = "业务线")
    @NotEmpty(message = "UPM00009")
    @Pattern(regexp = "^[^`~!@#$%^&*+=|{}':;',//[//].<>/?~！@#￥%……&*——+|{}【】‘；：”“’。，、？]*$", message = "UPM00076")
    @Length(max = 3, message = "UPM00077")
    private String platform;

    @ApiModelProperty(value = "操作人用户Id")
    private String operatorId;


}
