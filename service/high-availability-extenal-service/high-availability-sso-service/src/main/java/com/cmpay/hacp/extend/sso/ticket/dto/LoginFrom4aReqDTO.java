package com.cmpay.hacp.extend.sso.ticket.dto;

import com.cmpay.lemon.framework.desensitization.Desensitization;
import com.cmpay.lemon.framework.desensitization.Type;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;


/**
 * @Author: zhangning
 * @Date: 2020/04/26
 * 来自4A的用户登陆请求DTO
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class LoginFrom4aReqDTO extends BaseReqDTO {

    @ApiModelProperty(value = "用户名")
    @NotEmpty(message = "UPM00009")
    @Desensitization(Type.RIGHT)
    private String userName;

    @ApiModelProperty(value = "姓名")
    @NotEmpty(message = "UPM00009")
    @Desensitization(type = Type.CHINESE_NAME)
    private String fullName;

    @ApiModelProperty(value = "手机号")
    @NotEmpty(message = "UPM00009")
    @Pattern(regexp = "^1[0-9]{10}$", message = "UPM00009")
    @Desensitization(Type.MOBILE_NO)
    private String mobile;

}
