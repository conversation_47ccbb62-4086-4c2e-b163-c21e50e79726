package com.cmpay.hacp.extend.sso.ticket.dto;

import com.cmpay.lemon.common.log.LogIgnore;
import com.cmpay.lemon.framework.desensitization.Desensitization;
import com.cmpay.lemon.framework.desensitization.Type;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * @author: zhangning
 * @Date: 2020/02/15
 * 权限认证登陆请求DTO
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class LoginReqDTO extends BaseReqDTO {

    @ApiModelProperty(value = "用户名")
    @NotEmpty(message = "UPM00009")
    @Desensitization(Type.RIGHT)
    private String userName;

    @ApiModelProperty(value = "密码")
    @LogIgnore
    @NotEmpty(message = "UPM00009")
    private String password;


}
