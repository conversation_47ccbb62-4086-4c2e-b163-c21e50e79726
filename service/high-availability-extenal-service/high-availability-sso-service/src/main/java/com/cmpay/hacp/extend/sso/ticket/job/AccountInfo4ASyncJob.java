package com.cmpay.hacp.extend.sso.ticket.job;

import com.cmpay.hacp.extend.sso.ticket.service.BatchUserService;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 *
 */
@Slf4j
@EnableScheduling
public class AccountInfo4ASyncJob {

    private final BatchUserService batchUserService;

    public AccountInfo4ASyncJob(BatchUserService batchUserService) {
        this.batchUserService = batchUserService;
    }

    /**
     * 4A与统一工作台用户同步信息 定时任务【每日凌晨2点执行一次】
     */
    @Scheduled(cron = "${upms.user-sync-config.cron:0 0 2 * * ?}")
    @InitialLemonData
    public void run() {
        log.info("4A与统一工作台用户同步信息定时任务启动");
        this.batchUserService.queryAccountForSync(null, null, false);
    }
}
