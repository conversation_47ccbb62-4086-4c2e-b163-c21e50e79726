package com.cmpay.hacp.extend.sso.ticket.job;

import com.cmpay.hacp.extend.sso.ticket.service.ScimIamService;
import com.cmpay.lemon.framework.data.interceptor.InitialLemonData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * <AUTHOR>
 * 新4A同步任务
 */
@Slf4j
@EnableScheduling
public class ScimIamSyncJob {

    private final ScimIamService scimIamService;

    public ScimIamSyncJob(ScimIamService scimIamService) {
        this.scimIamService = scimIamService;
    }

    /**
     * 4A与统一工作台用户同步信息 定时任务【每日凌晨2点执行一次】
     */
    @Scheduled(cron = "${hacp.web.admin.scim-iam.cron:0 0 2 * * ?}")
    @InitialLemonData
    public void run() {
        log.info("ScimIamSyncJob running");
        this.scimIamService.sync();
        log.info("ScimIamSyncJob stop");
    }
}
