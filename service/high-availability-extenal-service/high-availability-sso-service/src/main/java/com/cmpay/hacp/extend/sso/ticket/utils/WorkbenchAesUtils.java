package com.cmpay.hacp.extend.sso.ticket.utils;


import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * JDK 1.8+ 提供了正式版本的Base64工具类
 * 低版本 JDK 可以使用 org.apache.commons.codec.binary.Base64 或 org.apache.tomcat.util.codec.binary.Base64 等代替
 */
public class WorkbenchAesUtils {
    private static final String ALGORITHM = "AES";
    private static final String PAD_MODE = "AES/CBC/NoPadding";

    /**
     * 加密
     *
     * @param data 加密数据
     * @param key  密钥
     * @return 密文
     */
    public static String encrypt(String data, String key) throws Exception {
        String ivString = key;
        //偏移量
        byte[] iv = ivString.getBytes();

        Cipher cipher = Cipher.getInstance(PAD_MODE);
        int blockSize = cipher.getBlockSize();
        byte[] dataBytes = data.getBytes();
        int length = dataBytes.length;
        //计算需填充长度
        if (length % blockSize != 0) {
            length = length + (blockSize - (length % blockSize));
        }
        byte[] plaintext = new byte[length];
        //填充
        System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), ALGORITHM);
        //设置偏移量参数
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encryptBytes = cipher.doFinal(plaintext);
        return Base64.getEncoder().encodeToString(encryptBytes);
    }
}
