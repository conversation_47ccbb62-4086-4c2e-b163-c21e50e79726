package com.cmpay.hacp.extend.sso.ticket.utils;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.Closeable;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * <tr>
 *
 * @author: Dev.Yi.Zeng
 * @date: 2021/3/19 14:33
 * @since: v1.0
 */
public class WorkbenchHttpUtils {
    private static final Logger logger = LoggerFactory.getLogger(WorkbenchHttpUtils.class);
    private static final String UTF8 = "UTF-8";

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static <T> T post(String url, Object body, List<Header> headers, boolean ssl, Class<T> responseClass) {
        if (logger.isDebugEnabled()) {
            logger.debug("post headers: {},  url: {}", headers, url);
        }

        CloseableHttpClient httpClient = getCloseableHttpClient(headers, ssl);

        HttpEntity httpEntity = EntityBuilder.create()
                .setBinary(writeObjectAsBytesSafely(body))
                .setContentEncoding(UTF8)
                .setContentType(ContentType.APPLICATION_JSON)
                .build();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(httpEntity);

        try {
            return executeMethod(httpClient, httpPost, responseClass);
        } catch (IOException e) {
            if (logger.isWarnEnabled()) {
                logger.warn("execute post fail, reason: {}", e.getLocalizedMessage());
            }
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        } finally {
            close(httpClient);
        }
        return null;
    }

    public static <T> T get(String url, List<Header> headers, boolean ssl, Class<T> responseClass) {
        if (logger.isDebugEnabled()) {
            logger.debug("get headers: {}, url: {}", headers, url);
        }

        CloseableHttpClient httpClient = getCloseableHttpClient(headers, ssl);

        HttpGet httpGet = new HttpGet(url);

        try {
            return executeMethod(httpClient, httpGet, responseClass);
        } catch (IOException e) {
            if (logger.isWarnEnabled()) {
                logger.warn("execute get fail, reason: {}", e.getLocalizedMessage());
            }
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        } finally {
            close(httpClient);
        }

        return null;
    }

    private static CloseableHttpClient getCloseableHttpClient(List<Header> headers, boolean ssl) {
        HttpClientBuilder clientBuilder = HttpClientBuilder.create();

        if (ssl) {
            clientBuilder.setSSLSocketFactory(getIgnoreSslConnectionFactory());
            clientBuilder.setSSLHostnameVerifier(new NoopHostnameVerifier());
        }

        if (JudgeUtils.isNotEmpty(headers)) {
            clientBuilder.setDefaultHeaders(headers);
        }

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000)
                .setConnectionRequestTimeout(30000)
                .build();
        clientBuilder.setDefaultRequestConfig(requestConfig);

        return clientBuilder.build();
    }

    private static SSLConnectionSocketFactory getIgnoreSslConnectionFactory() {
        final TrustStrategy acceptingTrustStrategy = (x509Certificates, authType) -> true;

        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
        } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }

        return new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());
    }

    private static byte[] writeObjectAsBytesSafely(Object body) {
        try {
            String json = objectMapper.writeValueAsString(body);
            return json.getBytes(StandardCharsets.UTF_8);
        } catch (JsonProcessingException e) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        return null;
    }

    private static <T> T executeMethod(HttpClient httpClient, HttpRequestBase request, Class<T> responseClass) throws IOException {
        HttpResponse httpResponse = httpClient.execute(request);
        if (HttpStatus.SC_OK == httpResponse.getStatusLine().getStatusCode()) {
            return objectMapper.readValue(httpResponse.getEntity().getContent(), responseClass);
        } else {
            if (logger.isErrorEnabled()) {
                logger.error("receive http status: {}, reason: {}",
                        httpResponse.getStatusLine().getStatusCode(), httpResponse.getStatusLine().getReasonPhrase());
            }
        }
        return null;
    }

    private static void close(Closeable client) {
        if (null != client) {
            try {
                client.close();
            } catch (IOException e) {
                // Ignore
            }
        }
    }

}
