package com.cmpay.hacp.inspection.application.execution;

import com.cmpay.hacp.extend.container.service.HacpKubesphereService;
import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.execution.gateway.K8sGateway;
import com.cmpay.hacp.inspection.domain.execution.model.*;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScript;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptRepository;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginParamRepository;
import com.cmpay.hacp.inspection.domain.task.model.ContainerResource;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class K8sExecutor implements InspectionExecutor {

    private final K8sGateway k8sGateway;
    private final PluginScriptRepository pluginScriptRepository;
    private final RulePluginParamRepository rulePluginParamRepository;
    private final RuleMatchingService ruleMatchingService;
    private final HacpKubesphereService hacpKubesphereService;

    @Override
    public InspectionResult execute(RuleExecution ruleExecution) {
        log.info("开始执行K8s脚本巡检，规则ID: {}, 插件ID: {}",
                ruleExecution.getRuleId(), ruleExecution.getPluginId());

        try {
            // 1. 获取脚本内容
            String pluginId = ruleExecution.getPluginId();
            PluginScript pluginScript = pluginScriptRepository.getByPluginId(pluginId);
            if (pluginScript == null) {
                log.error("插件脚本不存在，插件ID: {}", pluginId);
                return buildErrorResult("插件脚本不存在: " + pluginId);
            }

            // 2. 处理脚本参数替换
            String scriptContent = processScriptParameters(pluginScript.getScriptContent(), ruleExecution.getRuleId());

            // 3. 获取K8s连接配置
            K8sConnectionConfig k8sConfig = buildK8sConnectionConfig(ruleExecution);
            if (k8sConfig == null) {
                log.error("无法构建K8s连接配置，目标主机信息不完整");
                return buildErrorResult("K8s连接配置构建失败");
            }

            // 4. 构建脚本执行请求
            ScriptExecutionRequest request = buildScriptExecutionRequest(scriptContent, ruleExecution.getPluginType());

            // 5. 执行脚本
            log.debug("在Pod {}/{} 中执行脚本", k8sConfig.getNamespace(), k8sConfig.getPodName());
            ScriptExecutionResult result = k8sGateway.executeScript(k8sConfig, request);

            // 6. 处理执行结果
            return processExecutionResult(result, ruleExecution);

        } catch (Exception e) {
            log.error("K8s脚本执行异常，规则ID: {}", ruleExecution.getRuleId(), e);
            return buildErrorResult("脚本执行异常: " + e.getMessage());
        }
    }

    @Override
    public boolean supports(ExecutionContext context) {
        return (context.getPluginType() == PluginType.SHELL_SCRIPT ||
                context.getPluginType() == PluginType.PYTHON_SCRIPT) &&
                context.getResourceType() == ResourceType.CONTAINER;
    }

    /**
     * 处理脚本参数替换
     */
    private String processScriptParameters(String scriptContent, String ruleId) {
        // 获取规则插件参数
        List<RulePluginParam> rulePluginParams = rulePluginParamRepository.findByRuleId(ruleId);

        // 转换为Map<String, String>
        Map<String, String> paramMap = rulePluginParams != null ?
                rulePluginParams.stream()
                        .collect(Collectors.toMap(
                                RulePluginParam::getPluginParamName,
                                RulePluginParam::getPluginParamValue
                        )) :
                Collections.emptyMap();

        // 替换脚本中的参数
        String processedScript = scriptContent;
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            processedScript = processedScript.replace("${" + entry.getKey() + "}", entry.getValue());
        }

        log.debug("脚本参数替换完成，参数数量: {}", paramMap.size());
        return processedScript;
    }

    /**
     * 构建K8s连接配置
     */
    private K8sConnectionConfig buildK8sConnectionConfig(RuleExecution ruleExecution) {
        ContainerResource containerResource = ruleExecution.getContainerResource();
        if (containerResource == null) {
            log.error("无法构建K8s连接配置，容器资源信息为空");
            return null;
        }

        String cluster = containerResource.getCluster();
        String namespace = containerResource.getNamespace();
        String resourceName = containerResource.getResourceName();
        K8sConnectionConfig config = queryPodDynamically(cluster, namespace, resourceName);
        if (config != null) {
            return config;
        }

        log.error("无法构建K8s连接配置，所有方式都失败");
        return null;
    }

    /**
     * 动态查询Pod
     */
    private K8sConnectionConfig queryPodDynamically(String cluster, String namespace, String appName) {
        try {
            String workspaceId = TenantUtils.getWorkspaceId();
            if (!StringUtils.hasText(workspaceId)) {
                log.warn("无法获取工作空间ID，跳过动态Pod查询");
                return null;
            }

            // 查询Pod列表
            Map<String, List<String>> pods = hacpKubesphereService.queryPods(workspaceId, cluster, namespace, appName);
            if (pods == null || pods.isEmpty()) {
                log.warn("未查询到Pod列表，cluster: {}, namespace: {}", cluster, namespace);
                return null;
            }

            // 如果指定了appName，优先查找对应的Pod
            if (StringUtils.hasText(appName) && pods.containsKey(appName)) {
                List<String> appPods = pods.get(appName);
                if (!appPods.isEmpty()) {
                    String podName = appPods.get(0); // 取第一个Pod
                    log.info("动态查询到Pod: {}/{}/{}", cluster, namespace, podName);

                    return K8sConnectionConfig.builder()
                            .cluster(cluster)
                            .namespace(namespace)
                            .podName(podName)
                            .workingDirectory("/tmp")
                            .build();
                }
            }

            // 如果没有指定appName或找不到对应Pod，取第一个可用的Pod
            for (Map.Entry<String, List<String>> entry : pods.entrySet()) {
                List<String> podList = entry.getValue();
                if (!podList.isEmpty()) {
                    String podName = podList.get(0);
                    log.info("动态查询到默认Pod: {}/{}/{}", cluster, namespace, podName);

                    return K8sConnectionConfig.builder()
                            .cluster(cluster)
                            .namespace(namespace)
                            .podName(podName)
                            .workingDirectory("/tmp")
                            .build();
                }
            }

        } catch (Exception e) {
            log.warn("动态查询Pod失败", e);
        }

        return null;
    }

    /**
     * 构建脚本执行请求
     */
    private ScriptExecutionRequest buildScriptExecutionRequest(String scriptContent, PluginType pluginType) {
        ScriptExecutionRequest.ScriptType scriptType = pluginType == PluginType.SHELL_SCRIPT ?
                ScriptExecutionRequest.ScriptType.SHELL :
                ScriptExecutionRequest.ScriptType.PYTHON;

        return ScriptExecutionRequest.builder()
                .scriptContent(scriptContent)
                .scriptType(scriptType)
                .timeoutSeconds(60) // 默认60秒超时
                .workingDirectory("/tmp")
                .build();
    }

    /**
     * 处理执行结果
     */
    private InspectionResult processExecutionResult(ScriptExecutionResult result, RuleExecution ruleExecution) {
        boolean scriptExecutionSuccess = result.isSuccess();

        if (!scriptExecutionSuccess) {
            log.warn("脚本执行失败，规则ID: {}, 错误信息: {}",
                    ruleExecution.getRuleId(), result.getErrorMessage());
            return buildScriptFailureResult(result);
        }

        // 脚本执行成功，进行规则匹配
        log.debug("脚本执行成功，开始规则匹配，输出长度: {}",
                result.getStdout() != null ? result.getStdout().length() : 0);

        RuleMatchingResult ruleMatchingResult = ruleMatchingService.matchRule(
                ruleExecution.getRuleId(),
                ruleExecution.getPluginId(),
                result.getStdout()
        );

        boolean ruleMatchingSuccess = ruleMatchingResult != null && ruleMatchingResult.isSuccess();

        // 构建最终结果
        boolean overallSuccess = scriptExecutionSuccess && ruleMatchingSuccess;
        String message = buildResultMessage(scriptExecutionSuccess, ruleMatchingSuccess, ruleMatchingResult);
        String details = buildResultDetails(result, ruleMatchingResult);

        return InspectionResult.builder()
                .success(overallSuccess)
                .scriptExecutionSuccess(scriptExecutionSuccess)
                .ruleMatchingSuccess(ruleMatchingSuccess)
                .message(message)
                .details(details)
                .ruleMatchingResult(ruleMatchingResult)
                .pluginName(ruleExecution.getPluginName())
                .build();
    }

    /**
     * 构建错误结果
     */
    private InspectionResult buildErrorResult(String errorMessage) {
        return InspectionResult.builder()
                .success(false)
                .scriptExecutionSuccess(false)
                .ruleMatchingSuccess(false)
                .message("执行失败")
                .details(errorMessage)
                .build();
    }

    /**
     * 构建脚本执行失败结果
     */
    private InspectionResult buildScriptFailureResult(ScriptExecutionResult result) {
        return InspectionResult.builder()
                .success(false)
                .scriptExecutionSuccess(false)
                .ruleMatchingSuccess(false)
                .message("脚本执行失败")
                .details(String.format("退出码: %d, 错误信息: %s, 标准输出: %s",
                        result.getExitCode(),
                        result.getErrorMessage(),
                        result.getStdout()))
                .build();
    }

    /**
     * 构建结果消息
     */
    private String buildResultMessage(boolean scriptExecutionSuccess, boolean ruleMatchingSuccess,
                                      RuleMatchingResult ruleMatchingResult) {
        if (!scriptExecutionSuccess) {
            return "脚本执行失败";
        }

        if (!ruleMatchingSuccess) {
            return Optional.ofNullable(ruleMatchingResult)
                    .map(RuleMatchingResult::getRuleMatchingFieldResults)
                    .filter(results -> !results.isEmpty())
                    .map(results -> results.get(0))
                    .map(RuleMatchingResult.RuleMatchingFieldResult::getMessage)
                    .filter(StringUtils::hasText)
                    .orElse("规则匹配失败");
        }

        return "巡检通过";
    }

    /**
     * 构建结果详情
     */
    private String buildResultDetails(ScriptExecutionResult result, RuleMatchingResult ruleMatchingResult) {
        StringBuilder details = new StringBuilder();

        // 脚本执行信息
        details.append("=== 脚本执行结果 ===\n");
        details.append("执行状态: ").append(result.isSuccess() ? "成功" : "失败").append("\n");
        details.append("退出码: ").append(result.getExitCode()).append("\n");
        details.append("执行时间: ").append(result.getExecutionTime()).append("ms\n");

        if (StringUtils.hasText(result.getStdout())) {
            details.append("标准输出:\n").append(result.getStdout()).append("\n");
        }

        if (StringUtils.hasText(result.getStderr())) {
            details.append("错误输出:\n").append(result.getStderr()).append("\n");
        }

        // 规则匹配信息
        if (ruleMatchingResult != null) {
            details.append("\n=== 规则匹配结果 ===\n");
            details.append("匹配状态: ").append(ruleMatchingResult.isSuccess() ? "通过" : "不通过").append("\n");

            if (StringUtils.hasText(ruleMatchingResult.getRuleMatchingFieldResults().get(0).getMessage())) {
                details.append("匹配信息: ").append(ruleMatchingResult.getRuleMatchingFieldResults().get(0).getMessage()).append("\n");
            }

            if (StringUtils.hasText(ruleMatchingResult.getErrorMessage())) {
                details.append("错误信息: ").append(ruleMatchingResult.getErrorMessage()).append("\n");
            }

            if (StringUtils.hasText(ruleMatchingResult.getSuggest())) {
                details.append("治理建议: ").append(ruleMatchingResult.getSuggest()).append("\n");
            }
        }

        return details.toString();
    }
}