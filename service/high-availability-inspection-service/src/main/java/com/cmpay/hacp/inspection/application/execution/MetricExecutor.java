package com.cmpay.hacp.inspection.application.execution;

import com.cmpay.hacp.inspection.domain.execution.gateway.FireflyGateway;
import com.cmpay.hacp.inspection.domain.execution.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.execution.model.InspectionExecutor;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.firefly.IndicatorQueryResponse;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 指标执行器
 * 使用FireflyGateway执行萤火虫系统的查询并返回巡检结果
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MetricExecutor implements InspectionExecutor {

    private final FireflyGateway fireflyGateway;

    @Override
    public InspectionResult execute(RuleExecution ruleExecution) {
        try {
            log.info("Executing Firefly inspection for task: {}", ruleExecution.getTaskId());

            IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                    .startTime(ruleExecution.getStartTime())
                    .endTime(ruleExecution.getEndTime())
                    .indicatorType("host")
                    .indicatorName("cpu_usage_rate")
                    .pageNum(1)
                    .pageSize(1000)
                    .build();

            // 执行萤火虫系统查询
            IndicatorQueryResponse response = fireflyGateway.getIndicatorDataList(request);

            // 处理查询结果
            if (response.isSuccess() && response.getDataList() != null) {
                log.debug("Firefly query successful, result type: {}, result count: {}",
                        response.getDataList(),
                        response.getDataList().size());

                // 这里可以根据具体的业务逻辑处理查询结果
                // 例如：检查指标值是否符合预期，生成巡检报告等

                return InspectionResult.builder()
                        .success(true)
                        .message("Firefly查询执行成功")
                        .details("查询结果数量: " + response.getDataList().size())
                        .pluginName("FireflyExecutor")
                        .scriptExecutionSuccess(true)
                        .ruleMatchingSuccess(true)
                        .build();
            } else {
                log.warn("Firefly query failed or returned no data: {}", response.getMsgInfo());
                return InspectionResult.builder()
                        .success(false)
                        .message("Firefly查询失败: " + response.getMsgCd())
                        .pluginName("FireflyExecutor")
                        .scriptExecutionSuccess(false)
                        .ruleMatchingSuccess(false)
                        .build();
            }

        } catch (Exception e) {
            log.error("Error executing Firefly inspection for task: {}", ruleExecution.getTaskId(), e);
            return InspectionResult.builder()
                    .success(false)
                    .message("Firefly巡检执行异常: " + e.getMessage())
                    .pluginName("FireflyExecutor")
                    .scriptExecutionSuccess(false)
                    .ruleMatchingSuccess(false)
                    .build();
        }
    }

    @Override
    public boolean supports(ExecutionContext context) {
        return context.getPluginType() == PluginType.SYSTEM_BUILT;
    }
}
