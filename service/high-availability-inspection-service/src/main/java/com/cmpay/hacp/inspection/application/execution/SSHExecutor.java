package com.cmpay.hacp.inspection.application.execution;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.execution.gateway.SSHGateway;
import com.cmpay.hacp.inspection.domain.execution.model.*;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScript;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptRepository;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.inspection.domain.rule.model.RulePluginParam;
import com.cmpay.hacp.inspection.domain.rule.repository.RulePluginParamRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class SSHExecutor implements InspectionExecutor {
    private final SSHGateway sshGateway;
    private final PluginScriptRepository pluginScriptRepository;
    private final RulePluginParamRepository rulePluginParamRepository;
    private final RuleMatchingService ruleMatchingService;

    private static final String WITHOUT = "无";

    @Override
    public InspectionResult execute(RuleExecution ruleExecution) {
        String ruleId = ruleExecution.getRuleId();
        String pluginId = ruleExecution.getPluginId();
        log.info("Executing inspection script, Rule ID: {}, Plugin ID: {}, Target: {}", ruleId, pluginId, ruleExecution.getTargetHostList());

        PluginScript pluginScript = pluginScriptRepository.getByPluginId(pluginId);


        if (pluginScript == null) {
            log.error("Plugin script not found for pluginId: {}", pluginId);
            return InspectionResult.builder()
                    .success(false)
                    .scriptExecutionSuccess(false)
                    .ruleMatchingSuccess(false)
                    .message("Plugin script not found")
                    .details("Plugin script not found for pluginId: " + pluginId)
                    .build();
        }

        String scriptContent = pluginScript.getScriptContent();

        // 获取规则插件参数
        List<RulePluginParam> rulePluginParams = rulePluginParamRepository.findByRuleId(ruleId);

        // 转换为Map<String, String>
        Map<String, String> paramMap = rulePluginParams != null ?
                rulePluginParams.stream()
                        .collect(Collectors.toMap(
                                RulePluginParam::getPluginParamName,
                                RulePluginParam::getPluginParamValue
                        )) :
                Collections.emptyMap();

        // 替换脚本中的参数
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            scriptContent = scriptContent.replace("${" + entry.getKey() + "}", entry.getValue());
        }

        StringBuilder message = new StringBuilder();
        StringBuilder details = new StringBuilder();
        boolean overallSuccess = true;
        boolean scriptExecutionSuccess = true;
        boolean ruleMatchingSuccess = true;
        // 巡检规则匹配
        RuleMatchingResult ruleMatchingResult = null;

        List<TargetHost> targetHostList = ruleExecution.getTargetHostList();
        for (int i = 0; i < targetHostList.size(); i++) {
            // 执行远程脚本
            TargetHost targetHost = targetHostList.get(i);
            if(i != 0){
                message.append("========").append(targetHost.getHost()).append("========");
                details.append("========").append(targetHost.getHost()).append("========");
            }

            ScriptExecutionResult result = sshGateway.executeScript(
                    SshConnectionConfig.builder()
                            .host(targetHost.getHost())
                            .port(targetHost.getPort())
                            .username(targetHost.getUsername())
                            .password(targetHost.getPassword())
                            .privateKeyPath(targetHost.getPrivateKeyPath())
                            .privateKeyContent(targetHost.getPrivateKeyContent())
                            .build(),
                    ScriptExecutionRequest.builder()
                            .scriptContent(scriptContent)
                            .scriptType(ruleExecution.getPluginType() == PluginType.SHELL_SCRIPT ? ScriptExecutionRequest.ScriptType.SHELL : ScriptExecutionRequest.ScriptType.PYTHON)
                            .build()
            );
            scriptExecutionSuccess = result.isSuccess();
            log.debug("Script execution completed: success={}, exitCode={}", scriptExecutionSuccess, result.getExitCode());



            if (scriptExecutionSuccess && StringUtils.hasText(result.getStdout())) {
                try {
                    ruleMatchingResult = ruleMatchingService.matchRule(ruleId, pluginId, result.getStdout());
                    ruleMatchingSuccess = ruleMatchingResult.isSuccess();
                    log.debug("Rule matching completed: success={}, message={}, errormessage={}", ruleMatchingSuccess, ruleMatchingResult.getMessage(), ruleMatchingResult.getErrorMessage());
                } catch (Exception e) {
                    log.error("Error during rule matching for ruleId: {}, pluginId: {}", ruleId, pluginId, e);
                    ruleMatchingResult = RuleMatchingResult.builder()
                            .success(false)
                            .ruleId(ruleId)
                            .pluginId(pluginId)
                            .errorMessage("Rule matching error: " + e.getMessage())
                            .build();
                }
            } else {
                log.warn("Skipping rule matching due to script execution failure or empty output");
            }

            // 构建最终结果
            overallSuccess = scriptExecutionSuccess && ruleMatchingSuccess;
            message.append(buildResultMessage(scriptExecutionSuccess, ruleMatchingSuccess, ruleMatchingResult));
            details.append(buildResultDetails(result, ruleMatchingResult));
            if(!overallSuccess || !scriptExecutionSuccess || !ruleMatchingSuccess){
                break;
            }
        }






        return InspectionResult.builder()
                .success(overallSuccess)
                .scriptExecutionSuccess(scriptExecutionSuccess)
                .ruleMatchingSuccess(ruleMatchingSuccess)
                .message(message.toString())
                .details(details.toString())
                .ruleMatchingResult(ruleMatchingResult)
                .build();
    }

    @Override
    public boolean supports(ExecutionContext context) {
        return (context.getPluginType() == PluginType.SHELL_SCRIPT ||
                context.getPluginType() == PluginType.PYTHON_SCRIPT) &&
                context.getResourceType() == ResourceType.VIRTUAL_MACHINE;
    }

    /**
     * 构建结果消息
     */
    private String buildResultMessage(boolean scriptExecutionSuccess, boolean ruleMatchingSuccess, RuleMatchingResult ruleMatchingResult) {
        if (!scriptExecutionSuccess) {
            return "脚本执行失败";
        }

        if (!ruleMatchingSuccess) {
            if (ruleMatchingResult != null && StringUtils.hasText(ruleMatchingResult.getMessage())) {
                return "规则匹配失败: " + ruleMatchingResult.getErrorMessage();
            }
            return "规则匹配失败";
        }

        return "巡检通过";
    }

    /**
     * 构建结果详情
     */
    private String buildResultDetails(ScriptExecutionResult result, RuleMatchingResult ruleMatchingResult) {
        StringBuilder details = new StringBuilder();

        // 脚本执行结果
        details.append("=== 脚本执行结果 ===\n");
        details.append("执行状态: ").append(result.isSuccess() ? "成功" : "失败").append(CommonConstant.LINE_BREAK);
        details.append("退出码: ").append(result.getExitCode()).append(CommonConstant.LINE_BREAK);
        details.append("执行时间: ").append(result.getExecutionTime()).append("ms\n");

        if (StringUtils.hasText(result.getStdout())) {
            details.append("标准输出:\n").append(result.getStdout()).append(CommonConstant.LINE_BREAK);
        }

        if (StringUtils.hasText(result.getStderr())) {
            details.append("错误输出:\n").append(result.getStderr()).append(CommonConstant.LINE_BREAK);
        }

        // 规则匹配结果
        if (ruleMatchingResult != null) {
            details.append("\n=== 规则匹配结果 ===\n");
            details.append("匹配状态: ").append(ruleMatchingResult.isSuccess() ? "通过" : "失败").append("\n");

            if (StringUtils.hasText(ruleMatchingResult.getMessage())) {
                details.append("匹配信息: ").append(ruleMatchingResult.getMessage()).append("\n");
            }

            if (StringUtils.hasText(ruleMatchingResult.getErrorMessage())) {
                details.append("错误信息: ").append(ruleMatchingResult.getErrorMessage()).append("\n");
            }

            if (StringUtils.hasText(ruleMatchingResult.getSuggest())) {
                details.append("治理建议: ").append(ruleMatchingResult.getSuggest()).append(CommonConstant.LINE_BREAK);
            }
        }

        return details.toString();
    }
}
