package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultFieldType;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptResult;

import java.util.List;
import java.util.Map;

/**
 * 脚本结果解析服务接口
 * 用于解析巡检脚本的结构化输出
 */
public interface ScriptResultParserService {

    /**
     * 解析脚本输出为结构化数据
     *
     * @param scriptOutput     脚本输出内容
     * @param fieldDefinitions 字段定义列表
     * @return 解析后的字段值映射 (fieldName -> value)
     */
    Map<String, Object> parseScriptOutput(String scriptOutput, List<PluginScriptResult> fieldDefinitions);

    /**
     * 验证字段值类型是否匹配定义
     *
     * @param value     字段值
     * @param fieldType 字段类型
     * @return 是否匹配
     */
    boolean validateFieldType(Object value, ScriptResultFieldType fieldType);

    /**
     * 转换字段值为指定类型
     *
     * @param value     原始值
     * @param fieldType 目标类型
     * @return 转换后的值
     * @throws IllegalArgumentException 如果转换失败
     */
    Object convertFieldValue(Object value, ScriptResultFieldType fieldType);
}
