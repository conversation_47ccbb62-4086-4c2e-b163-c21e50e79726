package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.service.PerInspectionReportGenerationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncReportGenerationService {
    private final PerInspectionReportGenerationService perInspectionReportGenerationService;

    @Async("asyncInspectionReportExecutor")
    public void generatePerInspectionReportAsync(String executionId) {
        perInspectionReportGenerationService.generatePerInspectionReport(executionId);
    }
}
