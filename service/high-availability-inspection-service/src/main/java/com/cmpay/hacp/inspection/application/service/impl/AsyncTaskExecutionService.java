package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.service.InspectionTaskMonitoringService;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.execution.service.RuleExecutionDomainService;
import com.cmpay.hacp.inspection.domain.task.model.Resource;
import com.cmpay.hacp.inspection.domain.task.model.TaskRuleMapping;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRuleResourceRepository;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.cmpay.lemon.framework.security.SimpleUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 异步任务执行服务
 * 负责异步执行巡检任务
 */
@Slf4j
@Service
public class AsyncTaskExecutionService {
    private final InspectionTaskMonitoringService monitoringService;
    private final AsyncReportGenerationService asyncReportGenerationService;
    private final RuleExecutionDomainService ruleExecutionDomainService;
    private final TaskRuleResourceRepository taskRuleResourceRepository;
    private final Executor ruleExecutor;

    public AsyncTaskExecutionService(InspectionTaskMonitoringService monitoringService,
                                     AsyncReportGenerationService asyncReportGenerationService,
                                     RuleExecutionDomainService ruleExecutionDomainService,
                                     TaskRuleResourceRepository taskRuleResourceRepository,
                                     @Qualifier("asyncInspectionRuleExecutor") Executor executor) {

        this.monitoringService = monitoringService;
        this.asyncReportGenerationService = asyncReportGenerationService;
        this.ruleExecutionDomainService = ruleExecutionDomainService;
        this.taskRuleResourceRepository = taskRuleResourceRepository;
        this.ruleExecutor = executor;
    }

    /**
     * 异步执行任务
     *
     * @param taskId              任务ID
     * @param taskRuleMappingList 规则列表
     */
    @Async("asyncInspectionTaskExecutor")
    public void executeTaskAsync(String taskId,String workspaceId, String executionId, List<TaskRuleMapping> taskRuleMappingList) {
        log.info("Executing task asynchronously: {}", taskId);

        TenantUtils.setWorkspaceId(workspaceId);
        SecurityUtils.setLoginUserAfterLoginRequest(new SimpleUserInfo("system", "system", null));
        // 规则维度的成功失败统计
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        AtomicBoolean hasError = new AtomicBoolean(false);

        executeRulesInParallel(taskRuleMappingList, taskId, executionId, successCount, failCount, hasError);
        log.info("All rules executed for task: {}", taskId);

        // 更新任务状态为已完成
        boolean success = !hasError.get() && failCount.get() == 0;
        monitoringService.markTaskAsCompleted(executionId, success);

        // 异步生成巡检报告
        try {
            asyncReportGenerationService.generatePerInspectionReportAsync(executionId);
            log.info("Per-inspection report generation initiated for task: {}", taskId);
        } catch (Exception e) {
            log.error("Failed to initiate per-inspection report generation for task: {}", taskId, e);
            // 报告生成失败不影响任务执行状态
        }

        log.info("Task execution completed: {}, success: {}, successCount: {}, failCount: {}",
                taskId, success, successCount.get(), failCount.get());
    }

    public void executeRulesInParallel(List<TaskRuleMapping> taskRuleMappingList, String taskId, String executionId,
                                       AtomicInteger successCount, AtomicInteger failCount, AtomicBoolean hasError) {
        // 并发执行所有规则
        CompletableFuture.allOf(taskRuleMappingList.stream()
                .map(taskRuleMapping -> CompletableFuture
                        .runAsync(() -> executeRule(taskRuleMapping, taskId, executionId, successCount, failCount), ruleExecutor)
                        .exceptionally(throwable -> {
                            log.error("Error executing rule: {}", taskRuleMapping.getRuleId(), throwable);
                            hasError.set(true);
                            failCount.incrementAndGet();
                            return null;
                        })).toArray(CompletableFuture[]::new)).join();
    }

    private void executeRule(TaskRuleMapping taskRuleMapping, String taskId, String executionId,
                             AtomicInteger successCount, AtomicInteger failCount) {
        RuleExecution ruleExecution = buildRuleExecution(taskRuleMapping, taskId, executionId);
        ruleExecutionDomainService.executeRule(ruleExecution, successCount, failCount);
    }

    private RuleExecution buildRuleExecution(TaskRuleMapping taskRuleMapping, String taskId, String executionId) {
        RuleExecution ruleExecution = new RuleExecution();
        ruleExecution.setTaskId(taskId);
        ruleExecution.setExecutionId(executionId);
        ruleExecution.setRuleId(taskRuleMapping.getRuleId());

        List<Resource> resourceList = taskRuleResourceRepository.list(taskId, taskRuleMapping.getRuleId());
        ResourceType resourceType = resourceList.isEmpty()
                ? ResourceType.ENVIRONMENT_INDEPENDENT
                : resourceList.get(0).getResourceType();
        ruleExecution.setResourceType(resourceType);

        return ruleExecution;
    }
}
