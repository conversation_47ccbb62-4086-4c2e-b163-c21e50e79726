package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.service.InspectionCipherService;
import com.cmpay.hacp.inspection.domain.dict.repository.SystemDictRepository;
import com.cmpay.hacp.system.service.SubAppCipherService;
import com.cmpay.hacp.system.service.SubSystemCipherService;
import com.cmpay.hacp.utils.crypto.SM2EncryptorUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
public class InspectionCipherServiceImpl implements InspectionCipherService, SubAppCipherService {

    private final SystemDictRepository systemDictRepository;

    private final SubSystemCipherService subSystemCipherService;

    @Value(CommonConstant.SUB_CIPHER_TYPE_CONFIG)
    private String DICT_TYPE_INSPECTION_CIPHER;

    private final static String INSPECTION_DATA_PUBLIC = "publicKey";
    private final static String DICT_INSPECTION_DATA_PRIVATE = "privateKey";

    public InspectionCipherServiceImpl(SystemDictRepository systemDictRepository, @Lazy SubSystemCipherService subSystemCipherService) {
        this.systemDictRepository = systemDictRepository;
        this.subSystemCipherService = subSystemCipherService;
    }

    @Override
    public String dataEncrypt(String encryptData) {
        String sm2PublicKey = systemDictRepository.queryValueByTypeAndLabel(DICT_TYPE_INSPECTION_CIPHER,
                INSPECTION_DATA_PUBLIC);
        if(JudgeUtils.isNull(sm2PublicKey)){
            BusinessException.throwBusinessException(ErrorCodeEnum.DATA_PUBLIC_KEY_IS_NULL);
        }
        return SM2EncryptorUtil.encrypt(sm2PublicKey, encryptData);
    }

    @Override
    public String dataDecrypt(String decryptData) {
        String sm2PrivateKey = systemDictRepository.queryValueByTypeAndLabel(DICT_TYPE_INSPECTION_CIPHER,DICT_INSPECTION_DATA_PRIVATE);
        if(JudgeUtils.isNull(sm2PrivateKey)){
            BusinessException.throwBusinessException(ErrorCodeEnum.DATA_PRIVATE_KEY_IS_NULL);
        }
        return SM2EncryptorUtil.decrypt(sm2PrivateKey, decryptData);
    }

    @Override
    public String getSm4RandomSalt(String key) {
        return subSystemCipherService.getSm4RandomSalt(key, key);
    }

    @Override
    public String decryptSysDataBySm4AndSm2AndEncrypt(String key, String data) {
        String plaintext = subSystemCipherService.decryptDataBySm4AndSm2(key, data);
        return this.dataEncrypt(plaintext);
    }

    @Override
    public String getPrivateKey() {
        String sm2PublicKey = systemDictRepository.queryValueByTypeAndLabel(DICT_TYPE_INSPECTION_CIPHER, INSPECTION_DATA_PUBLIC);
        if(JudgeUtils.isNull(sm2PublicKey)){
            BusinessException.throwBusinessException(ErrorCodeEnum.DATA_PUBLIC_KEY_IS_NULL);
        }
        return sm2PublicKey;
    }
}
