package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.application.service.ScriptResultParserService;
import com.cmpay.hacp.inspection.domain.condition.model.RuleCondition;
import com.cmpay.hacp.inspection.domain.condition.model.RuleConditionGroupInfo;
import com.cmpay.hacp.inspection.domain.condition.model.RulePluginResult;
import com.cmpay.hacp.inspection.domain.condition.repository.RulePluginResultRepository;
import com.cmpay.hacp.inspection.domain.model.enums.ConditionLogic;
import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptResult;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptOutputFiledRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 规则匹配服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RuleMatchingServiceImpl implements RuleMatchingService {

    private final ScriptResultParserService scriptResultParserService;
    private final PluginScriptOutputFiledRepository pluginScriptOutputFiledRepository;
    private final RulePluginResultRepository  rulePluginResultRepository;

    @Override
    public RuleMatchingResult matchRule(String ruleId, String pluginId, String scriptOutput) {
        try {
            // 获取规则条件
            RulePluginResult rulePluginResult = rulePluginResultRepository.findByRuleId(ruleId, pluginId);
            if (rulePluginResult == null) {
                return RuleMatchingResult.builder()
                        .success(false)
                        .ruleId(ruleId)
                        .pluginId(pluginId)
                        .errorMessage("Rule condition not found for ruleId: " + ruleId + ", pluginId: " + pluginId)
                        .build();
            }

            List<RuleCondition> ruleConditions = rulePluginResult.getRuleConditions();
            StringBuilder successSb = new StringBuilder();
            StringBuilder errorSb = new StringBuilder();
            List<RuleMatchingResult.RuleMatchingFieldResult> results = ruleConditions.stream().map(ruleCondition -> {
                // 获取字段定义
                PluginScriptResult pluginScriptResult = pluginScriptOutputFiledRepository.queryByFieldName(ruleCondition.getPluginOutputFiledName(),
                        pluginId);

                if (pluginScriptResult == null) {
                    errorSb.append("Field definition not found for pluginOutputFiledName: ")
                            .append(ruleCondition.getPluginOutputFiledName())
                            .append(CommonConstant.LINE_BREAK);
                    return RuleMatchingResult.RuleMatchingFieldResult.builder()
                            .success(false)
                            .errorMessage("Field definition not found for pluginOutputFiledName: " + ruleCondition.getPluginOutputFiledName())
                            .build();
                }

                // 获取所有字段定义用于解析
                List<PluginScriptResult> allFieldDefinitions = pluginScriptOutputFiledRepository.listByPluginId(pluginId);
                // 解析脚本输出
                Map<String, Object> parsedValues = scriptResultParserService.parseScriptOutput(scriptOutput, allFieldDefinitions);

                // 匹配规则条件
                RuleMatchingResult.RuleMatchingFieldResult ruleMatchingFieldResult = matchParsedValues(parsedValues,
                        ruleCondition,
                        pluginScriptResult);
                if (!ruleMatchingFieldResult.isSuccess()) {
                    errorSb.append(ruleMatchingFieldResult.getErrorMessage())
                            .append(CommonConstant.LINE_BREAK);
                }else{
                    successSb.append(ruleMatchingFieldResult.getMessage())
                            .append(CommonConstant.LINE_BREAK);
                }
                return ruleMatchingFieldResult;
            }).collect(Collectors.toList());
            RuleConditionGroupInfo ruleConditionInfo = rulePluginResult.getRuleConditionInfo();
            ConditionLogic conditionLogic = ruleConditionInfo.getConditionLogic();
            long successCount = results.stream().filter(RuleMatchingResult.RuleMatchingFieldResult::isSuccess).count();
            boolean success = conditionLogic == ConditionLogic.OR ? successCount > 0 : successCount == ruleConditions.size();
            return RuleMatchingResult.builder()
                    .pluginId(pluginId)
                    .ruleId(ruleId)
                    .success( success)
                    .errorMessage(errorSb.toString())
                    .message(successSb.toString())
                    .suggest(ruleConditionInfo.getSuggest())
                    .ruleMatchingFieldResults(results)
                    .build();
        } catch (Exception e) {
            log.error("Error matching rule: ruleId={}, pluginId={}", ruleId, pluginId, e);
            return RuleMatchingResult.builder()
                    .success(false)
                    .ruleId(ruleId)
                    .pluginId(pluginId)
                    .errorMessage("Rule matching error: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public RuleMatchingResult.RuleMatchingFieldResult matchParsedValues(Map<String, Object> parsedValues,
            RuleCondition ruleCondition,
            PluginScriptResult fieldDefinition) {
        String fieldName = fieldDefinition.getFieldName();
        Object actualValue = parsedValues.get(fieldName);

        // 检查字段值是否存在
        if (actualValue == null) {
            return RuleMatchingResult.RuleMatchingFieldResult.builder()
                    .success(false)
                    .fieldName(fieldName)
                    .errorMessage("Field value not found in script output: " + fieldName)
                    .build();
        }

        // 评估字段条件
        boolean conditionMet = evaluateFieldCondition(actualValue, ruleCondition);

        // 构建匹配详情
        RuleMatchingResult.FieldMatchingDetail detail = RuleMatchingResult.FieldMatchingDetail.builder()
                .fieldName(fieldName)
                .actualValue(actualValue)
                .expectedValue(ruleCondition.getComparisonValue())
                .condition(buildConditionMessage(ruleCondition.getComparisonOperator()))
                .matched(conditionMet)
                .message(buildFieldMatchingMessage(actualValue, ruleCondition, conditionMet))
                .build();

        return RuleMatchingResult.RuleMatchingFieldResult.builder()
                .success(conditionMet)
                .fieldName(fieldName)
                .actualValue(actualValue)
                .expectedValue(ruleCondition.getComparisonValue())
                .condition(buildConditionMessage(ruleCondition.getComparisonOperator()))
                .message(buildRuleMatchingMessage(conditionMet, fieldName, actualValue, ruleCondition))
                .fieldMatchingDetails(Collections.singletonList(detail))
                .build();
    }

    @Override
    public boolean evaluateFieldCondition(Object fieldValue, RuleCondition ruleCondition) {
        try {
            // 转换字段值为BigDecimal进行数值比较
            String actualValue = String.valueOf(fieldValue);
            String expectedValue = ruleCondition.getComparisonValue();
            RuleComparisonOperator operator = ruleCondition.getComparisonOperator();

            if (actualValue == null || expectedValue == null || operator == null) {
                log.warn("Invalid values for condition evaluation: actual={}, expected={}, judge={}",
                        actualValue, expectedValue, operator);
                return false;
            }

            switch (operator) {
                case GREATER_THAN:
                    return bigDecimalCompareTo(actualValue, expectedValue) > 0;
                case LESS_THAN:
                    return bigDecimalCompareTo(actualValue, expectedValue) < 0;
                case GREATER_THAN_OR_EQUAL:
                    if (StrEQUAL(actualValue, expectedValue)) {
                        return true;
                    }
                    return bigDecimalCompareTo(actualValue, expectedValue) >= 0;
                case LESS_THAN_OR_EQUAL:
                    if (StrEQUAL(actualValue, expectedValue)) {
                        return true;
                    }
                    return bigDecimalCompareTo(actualValue, expectedValue) <= 0;
                case EQUAL:
                    return StrEQUAL(actualValue, expectedValue);
                default:
                    log.warn("Unsupported comparison operator: {}", operator);
                    return false;
            }

        } catch (Exception e) {
            log.error("Error evaluating field condition: fieldValue={}, ruleCondition={}", fieldValue, ruleCondition, e);
            return false;
        }
    }


    private boolean StrEQUAL(String val1, String val2) {
        return val1.equals(val2);
    }

    private int bigDecimalCompareTo(String val1, String val2) {
        BigDecimal actualValue = convertToBigDecimal(val1);
        BigDecimal expectedValue = convertToBigDecimal(val2);
        return actualValue.compareTo(expectedValue);
    }

    /**
     * 转换值为BigDecimal
     */
    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }

        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }

        try {
            String stringValue = value.toString().trim();
            // 移除可能的单位符号
            String cleanValue = stringValue.replaceAll("[^0-9.-]", "");
            return new BigDecimal(cleanValue);
        } catch (NumberFormatException e) {
            log.warn("Cannot convert value to BigDecimal: {}", value);
            return null;
        }
    }

    /**
     * 获取判断条件描述
     */
    private String buildConditionMessage(RuleComparisonOperator comparisonOperator) {
        return comparisonOperator != null ? comparisonOperator.getDesc() : "未知条件";
    }

    /**
     * 构建字段匹配消息
     */
    private String buildFieldMatchingMessage(Object actualValue, RuleCondition ruleCondition, boolean matched) {
        String condition = buildConditionMessage(ruleCondition.getComparisonOperator());
        String status = matched ? "满足" : "不满足";

        return String.format("字段值 %s %s %s，条件%s",
                actualValue, condition, ruleCondition.getComparisonValue(), status);
    }

    /**
     * 构建规则匹配消息
     */
    private String buildRuleMatchingMessage(boolean success, String fieldName, Object actualValue, RuleCondition ruleCondition) {
        String status = success ? "通过" : "失败";
        String condition = buildConditionMessage(ruleCondition.getComparisonOperator());

        return String.format("巡检规则%s：字段 %s 实际值 %s，期望 %s %s，结果：%s",
                status, fieldName, actualValue, condition, ruleCondition.getComparisonValue(), status);
    }
}
