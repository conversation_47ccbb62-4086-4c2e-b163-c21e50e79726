package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.application.service.ScriptResultParserService;
import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultFieldType;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptResult;
import com.cmpay.hacp.inspection.infrastructure.validation.JsonValidator;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 脚本结果解析服务实现
 */
@Slf4j
@Service
public class ScriptResultParserServiceImpl implements ScriptResultParserService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Map<String, Object> parseScriptOutput(String scriptOutput, List<PluginScriptResult> fieldDefinitions) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(scriptOutput) || fieldDefinitions == null || fieldDefinitions.isEmpty()) {
            log.warn("Script output or field definitions is empty");
            return result;
        }

        try {
            // 尝试解析为JSON格式
            if (JsonValidator.isValidJson(scriptOutput)) {
                result = parseJsonOutput(scriptOutput, fieldDefinitions);
            } else {
                // 解析为键值对格式 (key=value 或 key:value)
                result = parseKeyValueOutput(scriptOutput, fieldDefinitions);
            }

            log.debug("Parsed script output: {}", result);
            return result;

        } catch (Exception e) {
            log.error("Failed to parse script output: {}", scriptOutput, e);
            return result;
        }
    }

    /**
     * 解析JSON格式输出
     */
    private Map<String, Object> parseJsonOutput(String scriptOutput, List<PluginScriptResult> fieldDefinitions) {
        Map<String, Object> result = new HashMap<>();

        try {
            JsonNode jsonNode = objectMapper.readTree(scriptOutput);

            for (PluginScriptResult fieldDef : fieldDefinitions) {
                String fieldName = fieldDef.getFieldName();
                Object value = extractValueFromJson(jsonNode, fieldName);

                if (value != null) {
                    Object convertedValue = convertFieldValue(value, fieldDef.getFieldType());
                    result.put(fieldName, convertedValue);
                }
            }

        } catch (Exception e) {
            log.error("Failed to parse JSON output: {}", scriptOutput, e);
        }

        return result;
    }

    /**
     * 从JSON对象中提取值，支持嵌套字段 (如 cpu.usage)
     */
    private Object extractValueFromJson(JsonNode jsonNode, String fieldName) {
        if (!fieldName.contains(".")) {
            return extractSingleValue(jsonNode.get(fieldName));
        }

        String[] parts = fieldName.split("\\.");
        JsonNode current = jsonNode;

        for (String part : parts) {
            if (current != null && !current.isNull()) {
                current = current.get(part);
            } else {
                return null;
            }
        }

        return extractSingleValue(current);
    }

    /**
     * 从JsonNode中提取具体的Java对象值
     */
    private Object extractSingleValue(JsonNode node) {
        if (node == null || node.isNull()) {
            return null;
        }

        if (node.isBoolean()) {
            return node.booleanValue();
        } else if (node.isInt()) {
            return node.intValue();
        } else if (node.isLong()) {
            return node.longValue();
        } else if (node.isDouble() || node.isFloat()) {
            return node.doubleValue();
        } else if (node.isTextual()) {
            return node.textValue();
        } else if (node.isArray()) {
            // 如果需要处理数组，可以转换为List
            return objectMapper.convertValue(node, List.class);
        } else if (node.isObject()) {
            // 如果需要处理对象，可以转换为Map
            return objectMapper.convertValue(node, Map.class);
        }

        return node.toString();
    }

    /**
     * 解析键值对格式输出
     */
    private Map<String, Object> parseKeyValueOutput(String scriptOutput, List<PluginScriptResult> fieldDefinitions) {
        Map<String, Object> result = new HashMap<>();

        String[] lines = scriptOutput.split("\n");
        Map<String, String> keyValueMap = new HashMap<>();

        // 解析每一行为键值对
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            String[] parts = null;
            if (line.contains("=")) {
                parts = line.split("=", 2);
            } else if (line.contains(":")) {
                parts = line.split(":", 2);
            }

            if (parts != null && parts.length == 2) {
                String key = parts[0].trim();
                String value = parts[1].trim();
                keyValueMap.put(key, value);
            }
        }

        // 根据字段定义提取值
        for (PluginScriptResult fieldDef : fieldDefinitions) {
            String fieldName = fieldDef.getFieldName();
            String rawValue = keyValueMap.get(fieldName);

            if (rawValue != null) {
                try {
                    Object convertedValue = convertFieldValue(rawValue, fieldDef.getFieldType());
                    result.put(fieldName, convertedValue);
                } catch (Exception e) {
                    log.warn("Failed to convert field value: {} = {}", fieldName, rawValue, e);
                }
            }
        }

        return result;
    }

    @Override
    public boolean validateFieldType(Object value, ScriptResultFieldType fieldType) {
        if (value == null) {
            return false;
        }

        try {
            convertFieldValue(value, fieldType);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Object convertFieldValue(Object value, ScriptResultFieldType fieldType) {
        if (value == null) {
            return null;
        }

        String stringValue = value.toString().trim();

        switch (fieldType) {
            case NUMERIC:
                return convertToNumeric(stringValue);
            case STRING:
                return stringValue;
            case BOOLEAN:
                return convertToBoolean(stringValue);
            default:
                throw new IllegalArgumentException("Unsupported field type: " + fieldType);
        }
    }

    /**
     * 转换为数值类型
     */
    private BigDecimal convertToNumeric(String value) {
        try {
            // 移除可能的单位符号 (如 %, MB, GB 等)
            String cleanValue = value.replaceAll("[^0-9.-]", "");
            return new BigDecimal(cleanValue);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Cannot convert to numeric: " + value, e);
        }
    }

    /**
     * 转换为布尔类型
     */
    private Boolean convertToBoolean(String value) {
        String lowerValue = value.toLowerCase();

        if ("true".equals(lowerValue) || "1".equals(lowerValue) ||
                "yes".equals(lowerValue) || "on".equals(lowerValue) ||
                "enabled".equals(lowerValue) || "active".equals(lowerValue)) {
            return true;
        }

        if ("false".equals(lowerValue) || "0".equals(lowerValue) ||
                "no".equals(lowerValue) || "off".equals(lowerValue) ||
                "disabled".equals(lowerValue) || "inactive".equals(lowerValue)) {
            return false;
        }

        throw new IllegalArgumentException("Cannot convert to boolean: " + value);
    }
}
