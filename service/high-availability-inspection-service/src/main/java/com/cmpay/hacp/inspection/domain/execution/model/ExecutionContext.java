package com.cmpay.hacp.inspection.domain.execution.model;

import com.cmpay.hacp.inspection.domain.execution.model.valueobject.ResourceType;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.enums.ResourceExecutionStrategyEnum;
import lombok.Data;

/**
 * 执行上下文
 */
@Data
public class ExecutionContext {
    private PluginType pluginType;
    private ResourceType resourceType;
}
