package com.cmpay.hacp.inspection.domain.execution.service;

import com.cmpay.hacp.emergency.bo.SubHostInfoBO;
import com.cmpay.hacp.emergency.service.SysConfigHostService;
import com.cmpay.hacp.inspection.application.execution.TargetHost;
import com.cmpay.hacp.inspection.application.service.InspectionTaskMonitoringService;
import com.cmpay.hacp.inspection.domain.execution.model.InspectionExecutor;
import com.cmpay.hacp.inspection.domain.execution.model.aggregate.RuleExecution;
import com.cmpay.hacp.inspection.domain.model.enums.ResourceExecutionStrategyEnum;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import com.cmpay.hacp.inspection.domain.task.model.ContainerResource;
import com.cmpay.hacp.inspection.domain.task.model.Resource;
import com.cmpay.hacp.inspection.domain.task.model.VmResource;
import com.cmpay.hacp.inspection.domain.task.repository.TaskRuleResourceRepository;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 插件执行服务 - 领域服务
 */
@Service
@Slf4j
public class PluginExecutionDomainService {
    private final ExecutorDomainService executorDomainService;
    private final Executor executor;
    private final TaskRuleResourceRepository taskRuleResourceRepository;
    private final InspectionTaskMonitoringService monitoringService;
    private final SysConfigHostService sysConfigHostService;

    public PluginExecutionDomainService(ExecutorDomainService executorDomainService,
                                        @Qualifier("asyncInspectionHostExecutor") Executor executor, TaskRuleResourceRepository taskRuleResourceRepository, InspectionTaskMonitoringService monitoringService, SysConfigHostService sysConfigHostService) {
        this.executorDomainService = executorDomainService;
        this.executor = executor;
        this.taskRuleResourceRepository = taskRuleResourceRepository;
        this.monitoringService = monitoringService;
        this.sysConfigHostService = sysConfigHostService;
    }

    public void executeForResources(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount) {
        // 获取检查执行器
        InspectionExecutor inspectionExecutor = executorDomainService.selectOptimalExecutor(ruleExecution.getExecutionContext());

        List<Resource> resourceList = taskRuleResourceRepository.list(ruleExecution.getTaskId(), ruleExecution.getRuleId());
        if (resourceList.isEmpty()) {
            log.warn("No resources found for rule: {}", ruleExecution.getRuleId());
            return;
        }

        // 主机维度的成功失败统计
        AtomicInteger hostSuccessCount = new AtomicInteger(0);
        AtomicInteger hostFailCount = new AtomicInteger(0);

        try {
            // 为每个资源（主机）创建CompletableFuture任务
            List<CompletableFuture<InspectionResult>> futures = resourceList.stream()
                    .map(resource -> CompletableFuture.supplyAsync(() -> executePluginForHost(ruleExecution, resource,
                            inspectionExecutor), executor)) // 使用自定义线程池
                    .collect(Collectors.toList());

            // 等待所有主机执行完成并收集结果
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 统计主机维度和规则维度的结果
            boolean ruleSuccess = true;
            for (CompletableFuture<InspectionResult> future : futures) {
                try {
                    InspectionResult result = future.get();
                    if (result.isSuccess()) {
                        hostSuccessCount.incrementAndGet();
                    } else {
                        hostFailCount.incrementAndGet();
                        ruleSuccess = false;
                    }
                } catch (Exception e) {
                    log.error("Error getting inspection result", e);
                    hostFailCount.incrementAndGet();
                    ruleSuccess = false;
                }
            }

            // 规则维度统计：只有当所有主机都成功时，规则才算成功
            if (ruleSuccess) {
                successCount.incrementAndGet();
            } else {
                failCount.incrementAndGet();
            }

            log.info("Rule execution completed: {}, ruleSuccess: {}, hostSuccessCount: {}, hostFailCount: {}",
                    ruleExecution.getRuleId(), ruleSuccess, hostSuccessCount.get(), hostFailCount.get());

        } catch (Exception e) {
            log.error("Error executing rule: {} for task: {}", ruleExecution.getRuleId(), ruleExecution.getTaskId(), e);
            failCount.incrementAndGet();
        }


    }

    public void executeSystemMetric(RuleExecution ruleExecution, AtomicInteger successCount, AtomicInteger failCount) {
        // 获取检查执行器
        InspectionExecutor inspectionExecutor = executorDomainService.selectOptimalExecutor(ruleExecution.getExecutionContext());

        // 执行巡检
        InspectionResult result = inspectionExecutor.execute(ruleExecution);
    }

    private InspectionResult executePluginForHost(RuleExecution ruleExecution, Resource resource, InspectionExecutor inspectionExecutor) {
        try {
            ruleExecution.setStrategy(resource.getStrategy());
            if (resource instanceof VmResource) {
                List<TargetHost> targetHost = convertToTargetHostList((VmResource) resource);
                ruleExecution.setTargetHostList(targetHost);
            } else if (resource instanceof ContainerResource) {
                ruleExecution.setContainerResource((ContainerResource) resource);
            }

            log.info("Executing plugin: {} for resource: {} in rule: {}",
                    ruleExecution.getPluginId(), resource.getResourceName(), ruleExecution.getRuleId());

            // 执行巡检
            InspectionResult result = inspectionExecutor.execute(ruleExecution);

            // 按单一主机记录执行结果，包含资源信息
            monitoringService.recordRuleExecutionResult(ruleExecution.getTaskId(), ruleExecution.getExecutionId(), ruleExecution.getRuleId(), ruleExecution.getPluginId(), result,
                    resource.getResourceName(), resource.getResourceName());

            log.info("Plugin execution completed for {}: {}, resource: {}, success: {}",
                    resource.getResourceType(), resource.getResourceName(), resource.getResourceName(), result.isSuccess());

            return result;

        } catch (Exception e) {
            log.error("Error executing plugin: {} for resource: {}", ruleExecution.getPluginId(), resource.getResourceName(), e);

            // 记录失败结果
            InspectionResult failResult = InspectionResult.builder()
                    .success(false)
                    .message("Execution error")
                    .details(e.getMessage())
                    .pluginName(ruleExecution.getPluginName())
                    .build();

            monitoringService.recordRuleExecutionResult(ruleExecution.getTaskId(), ruleExecution.getExecutionId(), ruleExecution.getRuleId(), ruleExecution.getPluginId(), failResult,
                    resource.getResourceName(), resource.getResourceName());

            return failResult;
        }
    }

    private List<TargetHost> convertToTargetHostList(VmResource vmResource) {
        List<SubHostInfoBO> subHostInfoBOs = sysConfigHostService.queryHostList(Collections.singletonList(vmResource.getResourceId()), TenantUtils.getWorkspaceId());
        if (CollectionUtils.isEmpty(subHostInfoBOs)) {
            return new ArrayList<>();
        }
        if(vmResource.getStrategy().equals(ResourceExecutionStrategyEnum.RANDOM)){
            SubHostInfoBO targetHost = subHostInfoBOs
                    .get(Integer.parseInt(RandomUtil.getCharacterAndNumber(subHostInfoBOs.size() - 1)));
            subHostInfoBOs.clear();
            subHostInfoBOs.add(targetHost);
        }
        return subHostInfoBOs.stream().map(subHostInfoBO -> TargetHost.builder()
                .authType(StringUtils.isBlank(subHostInfoBO.getHostPassword()) ? TargetHost.AuthType.PRIVATE_KEY : TargetHost.AuthType.PASSWORD)
                .hostId(subHostInfoBO.getHostId().toString())
                .hostName(subHostInfoBO.getHostDesc())
                .host(subHostInfoBO.getHostAddress())
                .port(subHostInfoBO.getHostPort())
                .username(subHostInfoBO.getHostUsername())
                .password(subHostInfoBO.getHostPassword())
                .build())
                .collect(Collectors.toList());
    }
}
