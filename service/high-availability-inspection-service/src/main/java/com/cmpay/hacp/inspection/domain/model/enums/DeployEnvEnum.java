package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 部署环境
 */
@Getter
@Slf4j
public enum DeployEnvEnum {

    VM(1, "虚拟机(部署在虚拟机上的服务)"),
    CONTAINER(2, "容器(部署在K8s/Docker环境的服务)"),
    NONE(3, "环境无关")
    ;

    @JsonValue
    private final Integer code;
    private final String desc;


    DeployEnvEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    private static final Map<Integer, DeployEnvEnum> ENUM_MAP = Arrays.stream(DeployEnvEnum.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static DeployEnvEnum getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }

    public static List<DeployEnvEnum> getScriptDeployEnv() {
        return Arrays.asList(VM, CONTAINER);
    }

    public static List<DeployEnvEnum> getAllDeployEnv() {
        return Arrays.asList(VM, CONTAINER,NONE);
    }
}

