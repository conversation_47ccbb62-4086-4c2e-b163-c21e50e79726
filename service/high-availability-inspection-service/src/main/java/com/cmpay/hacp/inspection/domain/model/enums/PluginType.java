package com.cmpay.hacp.inspection.domain.model.enums;

import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 插件类型
 */
@Getter
@Slf4j
public enum PluginType {
    SYSTEM_BUILT(0,"系统内置",DeployEnvEnum.getAllDeployEnv()),
    SHELL_SCRIPT(1, "SHELL脚本",DeployEnvEnum.getScriptDeployEnv()),
    PYTHON_SCRIPT(2, "PYTHON脚本",DeployEnvEnum.getScriptDeployEnv()),
    ;

    @JsonValue
    private final Integer code;
    private final String desc;
    private final List<DeployEnvEnum> deployEnvs;

    PluginType(Integer code, String desc, List<DeployEnvEnum> deployEnvs) {
        this.code = code;
        this.desc = desc;
        this.deployEnvs = deployEnvs;
    }

    private static final Map<Integer, PluginType> ENUM_MAP = Arrays.stream(PluginType.values()).collect(HashMap::new, (m, v) -> m.put(v.code, v), HashMap::putAll);


    @JsonCreator
    public static PluginType getByCode(Integer code) {
        if(JudgeUtils.isNull(code)){
            return null;
        }
        if(!ENUM_MAP.containsKey(code)){
            log.error("enum code not exist in {}", code);
            return null;
        }
        return ENUM_MAP.get(code);
    }
}
