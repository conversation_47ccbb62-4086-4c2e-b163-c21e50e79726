package com.cmpay.hacp.inspection.domain.plugin.model;

import com.cmpay.hacp.inspection.domain.model.common.AuditInfo;
import com.cmpay.hacp.inspection.domain.model.enums.DeployEnvEnum;
import com.cmpay.hacp.inspection.domain.model.enums.PluginStatus;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检插件业务对象
 */
@Data
public class InspectionPlugin {
    /**
     * 插件ID（更新时使用）
     */
    private String pluginId;

    /**
     * 插件名称
     */
    private String name;

    /**
     * 插件类型 - SHELL_SCRIPT, PYTHON_SCRIPT, PAGE_ALIVE 等
     */
    private PluginType type;

    /**
     * 插件状态(0禁用，1启用)
     */
    private PluginStatus status;

    /**
     * 支持环境
     */
    private List<DeployEnvEnum> deployEnvs;

    /**
     * 插件描述
     */
    private String description;
    /**
     * 插件类型详细配置
     */
    private PluginConfig pluginConfig;

    /**
     * 标签ID列表
     */
    private List<Long> tagIds;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    private AuditInfo auditInfo;

    private String key;
}
