package com.cmpay.hacp.inspection.domain.plugin.model;

import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PluginConfigScript extends PluginConfig{

    /**
     * 输出字段定义
     */
    @Schema(description = "输出字段定义列表")
    private List<PluginScriptResult> results;

    /**
     * 参数设置列表
     */
    @Schema(description = "参数设置列表")
    private List<PluginScriptParameter> parameters;

    @Schema(description = "脚本内容", required = true, example = "#!/bin/bash\necho \"CPU使用率检查\"")
    private String scriptContent;

    @Schema(description = "脚本输出类型：1-结构化，2-纯文本", example = "1")
    private ScriptResultType scriptResultType;
}
