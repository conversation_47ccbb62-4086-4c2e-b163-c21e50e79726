package com.cmpay.hacp.inspection.domain.plugin.repository;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptResult;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface PluginScriptOutputFiledRepository {
    void saveBatch(List<PluginScriptResult> pluginScriptResultList, @NotNull String pluginId);

    List<PluginScriptResult> listByPluginId(@NotNull String pluginId);

    void updateBatchByName(List<PluginScriptResult> pluginScriptResultList, @NotNull String pluginId);

    void removeByFieldNames(List<String> fieldNames, @NotNull String pluginId);

    void removeByPluginId(@NotNull String pluginId);

    PluginScriptResult queryByFieldName(@NotNull String fieldName, @NotNull String pluginId);
}
