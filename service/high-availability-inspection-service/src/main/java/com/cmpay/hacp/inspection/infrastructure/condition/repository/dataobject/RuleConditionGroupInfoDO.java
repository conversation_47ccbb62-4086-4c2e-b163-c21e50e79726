package com.cmpay.hacp.inspection.infrastructure.condition.repository.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.ConditionLogic;
import com.cmpay.hacp.inspection.domain.model.enums.RuleCheckPeakEnum;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.BaseInsertDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_rule_condition_group_info")
public class RuleConditionGroupInfoDO extends BaseInsertDO {

    private String conditionGroupId;

    /**
     * 条件逻辑
     */
    private ConditionLogic conditionLogic;

    /**
     * 持续时间（秒）
     */
    private Integer duration;

    /**
     * 检查间隔（秒）
     */
    private Integer checkInterval;

    /**
     * 仅检查峰值
     * {@link RuleCheckPeakEnum}
     */
    private Integer checkPeak;

    /**
     * 检查特定进程（逗号分隔）
     */
    private String specificProcesses;

    /**
     * 治理建议
     */
    private String suggest;
}
