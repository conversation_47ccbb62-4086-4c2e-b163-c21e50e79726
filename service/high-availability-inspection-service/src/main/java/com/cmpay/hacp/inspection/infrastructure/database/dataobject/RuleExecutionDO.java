package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 巡检规则执行记录实体类
 * 记录巡检任务中每个规则在各个资源上的具体执行状态
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_rule_execution")
public class RuleExecutionDO extends BaseDO {
    /**
     * 任务执行ID
     */
    private String taskExecutionId;
    
    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 资源ID
     */
    private String resource;

    /**
     * 资源名称
     */
    private String resourceName;
    
    /**
     * 插件ID
     */
    private String pluginId;
    
    /**
     * 执行状态 0:失败 1:成功
     */
    private String executionStatus;

    /**
     * 匹配状态 0:失败 1:成功
     */
    private boolean matchStatus;

    /**
     * 执行时间
     */
    private LocalDateTime executionTime;
    
    /**
     * 执行结果详情
     */
    private String executionResult;
    
    /**
     * 插件名称
     */
    private String pluginName;
    
    /**
     * 执行耗时(毫秒)
     */
    private Long executionDuration;
}
