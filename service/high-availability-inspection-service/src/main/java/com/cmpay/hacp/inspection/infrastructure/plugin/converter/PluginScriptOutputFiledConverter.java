package com.cmpay.hacp.inspection.infrastructure.plugin.converter;

import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptResult;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginScriptOutputFieldDO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PluginScriptOutputFiledConverter {
    @IgnoreAuditFields
    PluginScriptOutputFieldDO toPluginScriptOutputFieldDO(PluginScriptResult scriptResult, String pluginId);

    List<PluginScriptResult> toPluginScriptResultList(List<PluginScriptOutputFieldDO> list);

    PluginScriptResult toPluginScriptResult(PluginScriptOutputFieldDO one);
}
