package com.cmpay.hacp.inspection.infrastructure.plugin.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.plugin.model.PluginScriptResult;
import com.cmpay.hacp.inspection.domain.plugin.repository.PluginScriptOutputFiledRepository;
import com.cmpay.hacp.inspection.infrastructure.plugin.converter.PluginScriptOutputFiledConverter;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.dataobject.PluginScriptOutputFieldDO;
import com.cmpay.hacp.inspection.infrastructure.plugin.repository.mapper.PluginScriptOutputFiledMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 插件脚本结构化输出Repository
 */
@Repository
@RequiredArgsConstructor
public class PluginScriptOutputFiledRepositoryImpl extends CrudRepository<PluginScriptOutputFiledMapper, PluginScriptOutputFieldDO> implements PluginScriptOutputFiledRepository {
    private final PluginScriptOutputFiledConverter pluginScriptOutputFiledConverter;
    @Override
    public void saveBatch(List<PluginScriptResult> pluginScriptResultList, String pluginId) {
        if (pluginScriptResultList == null || pluginScriptResultList.isEmpty()) {
            return;
        }

        List<PluginScriptOutputFieldDO> results = pluginScriptResultList.stream()
                .map(scriptResult -> pluginScriptOutputFiledConverter.toPluginScriptOutputFieldDO(scriptResult, pluginId))
                .collect(Collectors.toList());

        this.saveBatch(results, 10);
    }

    @Override
    public List<PluginScriptResult> listByPluginId(String pluginId) {
        return pluginScriptOutputFiledConverter.toPluginScriptResultList(this.list(
                Wrappers.lambdaQuery(PluginScriptOutputFieldDO.class)
                        .eq(PluginScriptOutputFieldDO::getPluginId, pluginId)));
    }

    @Override
    public void updateBatchByName(List<PluginScriptResult> pluginScriptResultList, String pluginId) {
        if (pluginScriptResultList == null || pluginScriptResultList.isEmpty()) {
            return;
        }

        List<PluginScriptOutputFieldDO> results = pluginScriptResultList.stream()
                .map(scriptResult -> pluginScriptOutputFiledConverter.toPluginScriptOutputFieldDO(scriptResult, pluginId))
                .collect(Collectors.toList());

        this.updateBatchById(results, 10);
    }

    @Override
    public void removeByFieldNames(List<String> fieldNames, String pluginId) {
        this.remove(Wrappers.lambdaQuery(PluginScriptOutputFieldDO.class)
                .eq(PluginScriptOutputFieldDO::getPluginId, pluginId)
                .in(PluginScriptOutputFieldDO::getFieldName, fieldNames));
    }

    @Override
    public void removeByPluginId(String pluginId) {
        this.remove(Wrappers.lambdaQuery(PluginScriptOutputFieldDO.class)
                .eq(PluginScriptOutputFieldDO::getPluginId, pluginId));
    }

    @Override
    public PluginScriptResult queryByFieldName(String fieldName, String pluginId) {
        return pluginScriptOutputFiledConverter.toPluginScriptResult(this.getOne(
                Wrappers.lambdaQuery(PluginScriptOutputFieldDO.class)
                        .eq(PluginScriptOutputFieldDO::getPluginId, pluginId)
                        .eq(PluginScriptOutputFieldDO::getFieldName, fieldName)));
    }
}
