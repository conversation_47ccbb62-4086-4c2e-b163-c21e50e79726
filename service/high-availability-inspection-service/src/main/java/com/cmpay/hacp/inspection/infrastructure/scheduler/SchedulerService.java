package com.cmpay.hacp.inspection.infrastructure.scheduler;

import com.cmpay.hacp.constant.TenantConstant;
import com.cmpay.hacp.inspection.domain.model.task.ScheduleConfig;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SchedulerService {
    private final Scheduler scheduler;

    public SchedulerService(Scheduler scheduler) {
        this.scheduler = scheduler;
    }

    /**
     * 创建定时巡检任务
     */
    public void scheduleInspectionTask(String taskId, String cronExpression) throws SchedulerException {
        // 创建JobDetail
        JobDetail jobDetail = JobBuilder.newJob(InspectionTaskJob.class)
                .withIdentity("inspection-" + taskId, "inspection-tasks")
                .usingJobData("taskId", taskId)
                .storeDurably()
                .build();

        // 创建Trigger
        CronTrigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("trigger-" + taskId, "inspection-triggers")
                .forJob(jobDetail)
                .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                .build();

        // 调度任务
        if (scheduler.checkExists(jobDetail.getKey())) {
            scheduler.rescheduleJob(trigger.getKey(), trigger);
            log.info("Updating inspection task schedule: {}, cron: {}", taskId, cronExpression);
        } else {
            scheduler.scheduleJob(jobDetail, trigger);
            log.info("Creating inspection task schedule: {}, cron: {}", taskId, cronExpression);
        }
    }

    public void scheduleInspectionTask(String taskId, ScheduleConfig scheduleConfig) throws SchedulerException {
        // 创建JobDetail
        JobDetail jobDetail = JobBuilder.newJob(InspectionTaskJob.class)
                .withIdentity("inspection-" + taskId, "inspection-tasks")
                .usingJobData("taskId", taskId)
                .usingJobData(TenantConstant.WORKSPACE_ID, TenantUtils.getWorkspaceId())
                .storeDurably()
                .build();
        // 创建Trigger
        Trigger trigger = ScheduleConfigQuartzAdapter.createTrigger("trigger-" + taskId, scheduleConfig);

        // 调度任务
        boolean exists = scheduler.checkExists(jobDetail.getKey());
        if (exists) {
            scheduler.rescheduleJob(trigger.getKey(), trigger);
            log.info("Updating inspection task schedule: {}, scheduleConfig: {}", taskId, scheduleConfig);
        } else {
            scheduler.scheduleJob(jobDetail, trigger);
            log.info("Creating inspection task schedule: {}, scheduleConfig: {}", taskId, scheduleConfig);
        }
    }

    /**
     * 暂停任务
     */
    public void pauseTask(Long taskId) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey("inspection-task-" + taskId, "inspection-tasks");
        if (scheduler.checkExists(jobKey)) {
            scheduler.pauseJob(jobKey);
            log.info("Pausing inspection task: {}", taskId);
        }
    }

    /**
     * 恢复任务
     */
    public void resumeTask(Long taskId) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey("inspection-task-" + taskId, "inspection-tasks");
        if (scheduler.checkExists(jobKey)) {
            scheduler.resumeJob(jobKey);
            log.info("Resuming inspection task: {}", taskId);
        }
    }

    /**
     * 删除任务
     */
    public void deleteTask(Long taskId) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey("inspection-task-" + taskId, "inspection-tasks");
        if (scheduler.checkExists(jobKey)) {
            scheduler.deleteJob(jobKey);
            log.info("Deleting inspection task: {}", taskId);
        }
    }

    /**
     * 立即执行一次任务
     */
    public void executeTaskNow(Long taskId) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey("inspection-task-" + taskId, "inspection-tasks");
        if (scheduler.checkExists(jobKey)) {
            scheduler.triggerJob(jobKey);
            log.info("Executing inspection task immediately: {}", taskId);
        }
    }
}
