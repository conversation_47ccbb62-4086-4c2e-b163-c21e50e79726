package com.cmpay.hacp.inspection.infrastructure.task.converter;

import com.cmpay.hacp.inspection.domain.task.model.ContainerResource;
import com.cmpay.hacp.inspection.domain.task.model.Resource;
import com.cmpay.hacp.inspection.domain.task.model.VmResource;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.task.repository.dataobject.TaskRuleResourceDO;
import com.cmpay.hacp.utils.JsonUtil;
import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TaskRuleResourceConverter {
    List<Resource> toResourceList(List<TaskRuleResourceDO> taskRuleResourceDOList);

    @IgnoreAuditFields
    @Mapping(target = "taskId", ignore = true)
    @Mapping(target = "ruleId", ignore = true)
    @Mapping(target = "resource", ignore = true)
    TaskRuleResourceDO toTaskRuleResourceDO(Resource resource);

    @AfterMapping
    default void afterToTaskRuleResourceDO(Resource resource, @MappingTarget TaskRuleResourceDO resourceDO) {
        if (resource == null) return;
        resourceDO.setResource(JsonUtil.objToStr(resource));
    }

    @Mapping(target = "resourceName", ignore = true)
    Resource toResource(TaskRuleResourceDO resourceDO);

    @ObjectFactory
    default Resource createResource(TaskRuleResourceDO resourceDO) {
        if (resourceDO == null) return null;

        Resource result;
        switch (resourceDO.getResourceType()) {
            case VIRTUAL_MACHINE:
                result = JsonUtil.strToObject(resourceDO.getResource(),VmResource.class);
                break;
            case CONTAINER:
                result = JsonUtil.strToObject(resourceDO.getResource(),ContainerResource.class);
                break;
            default:
                throw new IllegalArgumentException("Unknown schedule type: " + resourceDO.getResourceType());
        }

        return result;
    }
}
