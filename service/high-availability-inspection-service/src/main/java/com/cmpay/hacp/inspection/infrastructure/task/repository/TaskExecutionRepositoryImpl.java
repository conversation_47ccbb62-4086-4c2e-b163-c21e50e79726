package com.cmpay.hacp.inspection.infrastructure.task.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.task.repository.TaskExecutionRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.TaskExecutionMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 任务执行状态Repository
 */
@Repository
public class TaskExecutionRepositoryImpl extends CrudRepository<TaskExecutionMapper, TaskExecutionDO> implements TaskExecutionRepository {
    @Override
    public List<String> listTaskIdByTaskStatus(ExecutionStatus executionStatus) {
        List<TaskExecutionDO> list = this.list(Wrappers.lambdaQuery(TaskExecutionDO.class)
                .select(TaskExecutionDO::getTaskId)
                .eq(TaskExecutionDO::getExecutionStatus, executionStatus)
                .groupBy(TaskExecutionDO::getTaskId));
        return Optional.ofNullable(list).orElse(new ArrayList<>()).stream().map(TaskExecutionDO::getTaskId).collect(Collectors.toList());
    }

    @Override
    public ExecutionStatus queryStatusByTaskId(String taskId) {
        TaskExecutionDO taskExecutionDO = this.getOne(Wrappers.lambdaQuery(TaskExecutionDO.class)
                .in(TaskExecutionDO::getTaskId, taskId)
                .orderByAsc(TaskExecutionDO::getCreatedTime)
                .last("LIMIT 1"));
        return Optional.ofNullable(taskExecutionDO).map(TaskExecutionDO::getExecutionStatus).orElse(null);
    }
}
