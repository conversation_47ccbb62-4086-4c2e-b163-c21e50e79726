<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.ISystemDepartmentExtDao">
    <select id="findDeptId" parameterType="java.lang.String" resultType="java.lang.String">
        select
        dept_id
        from sys_dept
        <where>
            <if test="deptName != null and deptName != ''">
                and dept_name = #{deptName,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_time asc
    </select>
</mapper>