<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.ISystemMenuExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.MenuDO">
        <id column="menu_id" property="menuId" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="perms" property="perms" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="BIGINT"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="meta" property="meta" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="redirect" property="redirect" jdbcType="VARCHAR"/>
        <result column="en_name" property="enName" jdbcType="VARCHAR"/>
        <result column="parent_name" property="parentName" jdbcType="VARCHAR"/>
        <result column="hide_title" property="hideTitle" jdbcType="BOOLEAN"/>
        <result column="hidden" property="hidden" jdbcType="BOOLEAN"/>
        <result column="hide_children" property="hideChildren" jdbcType="BOOLEAN"/>
        <result column="keepalive" property="keepalive" jdbcType="BOOLEAN"/>
        <result column="hide_page_title_bar" property="hidePageTitleBar" jdbcType="BOOLEAN"/>
    </resultMap>

    <sql id="Base_Column_List">
        menu_id,
        parent_id,
        name,
        app_id,
        url,
        perms,
        type,
        icon,
        order_num,
        create_user_id,
        create_time,
        modify_time,
        meta,
        component,
        redirect,
        en_name,
        parent_name,
        hide_title,
        hidden,
        hide_children,
        keepalive,
        hide_page_title_bar
    </sql>


    <select id="getSystemMenus" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_menu
        where menu_id in (SELECT t2.menu_id
        FROM sys_role t1
        LEFT JOIN sys_role_menu t2 ON t1.role_id = t2.role_id
        WHERE t1.role_name = #{roleName,jdbcType=VARCHAR})
        order by menu_id asc, parent_id asc, order_num asc
    </select>
</mapper>
