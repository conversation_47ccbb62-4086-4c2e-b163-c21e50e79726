<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.ILoginHistoryLogExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.LoginHistoryLogDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="login_ip" property="loginIp" jdbcType="VARCHAR"/>
        <result column="login_terminal" property="loginTerminal" jdbcType="VARCHAR"/>
        <result column="login_from" property="loginFrom" jdbcType="VARCHAR"/>
        <result column="login_date" property="loginDate" jdbcType="DATE"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="is_use" property="isUse" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , user_id, user_name, name, mobile, login_ip, login_terminal, login_from, login_date,
        login_time, request_id, is_use
    </sql>

    <sql id="As_Base_Column_List">
        t1
        .
        id
        ,  t1.user_id,  t1.user_name,  t1.name,  t1.mobile,  t1.login_ip,  t1.login_terminal,  t1.login_from,  t1.login_date,
        t1.login_time,  t1.request_id,  t1.is_use
    </sql>

    <select id="queryLoginHistoryLog" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_login_history_log
        <where>
            <if test="loginHistoryLog.userId != null and loginHistoryLog.userId != ''">
                and user_id = #{loginHistoryLog.userId,jdbcType=VARCHAR}
            </if>
            <if test="loginHistoryLog.userName != null and loginHistoryLog.userName != ''">
                and user_name = #{loginHistoryLog.userName,jdbcType=VARCHAR}
            </if>
            <if test="loginHistoryLog.name != null and loginHistoryLog.name != ''">
                and name = #{loginHistoryLog.name,jdbcType=VARCHAR}
            </if>
            <if test="loginHistoryLog.mobile != null and loginHistoryLog.mobile != ''">
                and mobile = #{loginHistoryLog.mobile,jdbcType=INTEGER}
            </if>
            <if test="beginDate != null and endDate != null">
                and (login_time between #{beginDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR} )
            </if>
        </where>
        order by login_time desc
    </select>
    <select id="queryLoginLatestInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="As_Base_Column_List"/>
        FROM
        sys_login_latest_info t2
        INNER JOIN sys_login_history_log t1
        ON t2.pid = t1.id
        AND t2.user_id = #{userId,jdbcType=VARCHAR}
    </select>

</mapper>
