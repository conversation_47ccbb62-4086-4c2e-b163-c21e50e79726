<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.ILoginLatestInfoDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.LoginLatestInfoDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="latest_date" property="latestDate" jdbcType="DATE"/>
        <result column="latest_time" property="latestTime" jdbcType="TIMESTAMP"/>
        <result column="first_date" property="firstDate" jdbcType="DATE"/>
        <result column="first_time" property="firstTime" jdbcType="TIMESTAMP"/>
        <result column="is_use" property="isUse" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , pid, user_id, latest_date, latest_time, first_date, first_time, is_use
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_login_latest_info
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from sys_login_latest_info
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.system.entity.LoginLatestInfoDO">
        insert into sys_login_latest_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="pid != null">
                pid,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="latestDate != null">
                latest_date,
            </if>
            <if test="latestTime != null">
                latest_time,
            </if>
            <if test="firstDate != null">
                first_date,
            </if>
            <if test="firstTime != null">
                first_time,
            </if>
            <if test="isUse != null">
                is_use,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="pid != null">
                #{pid,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="latestDate != null">
                #{latestDate,jdbcType=DATE},
            </if>
            <if test="latestTime != null">
                #{latestTime,jdbcType=TIMESTAMP},
            </if>
            <if test="firstDate != null">
                #{firstDate,jdbcType=DATE},
            </if>
            <if test="firstTime != null">
                #{firstTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isUse != null">
                #{isUse,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.system.entity.LoginLatestInfoDO">
        update sys_login_latest_info
        <set>
            <if test="pid != null">
                pid = #{pid,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="latestDate != null">
                latest_date = #{latestDate,jdbcType=DATE},
            </if>
            <if test="latestTime != null">
                latest_time = #{latestTime,jdbcType=TIMESTAMP},
            </if>
            <if test="firstDate != null">
                first_date = #{firstDate,jdbcType=DATE},
            </if>
            <if test="firstTime != null">
                first_time = #{firstTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isUse != null">
                is_use = #{isUse,jdbcType=SMALLINT},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.system.entity.LoginLatestInfoDO">
        select
        <include refid="Base_Column_List"/>
        from sys_login_latest_info
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="pid != null">
                and pid = #{pid,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="latestDate != null">
                and latest_date = #{latestDate,jdbcType=DATE}
            </if>
            <if test="latestTime != null">
                and latest_time = #{latestTime,jdbcType=TIMESTAMP}
            </if>
            <if test="firstDate != null">
                and first_date = #{firstDate,jdbcType=DATE}
            </if>
            <if test="firstTime != null">
                and first_time = #{firstTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isUse != null">
                and is_use = #{isUse,jdbcType=SMALLINT}
            </if>
        </where>
    </select>
</mapper>
