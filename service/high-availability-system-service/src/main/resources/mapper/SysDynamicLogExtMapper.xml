<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.ISysDynamicLogExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.SysDynamicLogDO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="request_id" property="requestId" jdbcType="VARCHAR" />
        <result column="operator" property="operator" jdbcType="VARCHAR" />
        <result column="operation_action" property="operationAction" jdbcType="VARCHAR" />
        <result column="execution_target" property="executionTarget" jdbcType="VARCHAR" />
        <result column="operator_ip" property="operatorIp" jdbcType="VARCHAR" />
        <result column="operator_time" property="operatorTime" jdbcType="TIMESTAMP" />
        <result column="operator_status" property="operatorStatus" jdbcType="VARCHAR" />
        <result column="data_size" property="dataSize" jdbcType="BIGINT" />
        <result column="data_size_type" property="dataSizeType" jdbcType="VARCHAR" />
        <result column="application_name" property="applicationName" jdbcType="VARCHAR" />
        <result column="data_it" property="dataIt" jdbcType="VARCHAR" />
        <result column="data_path" property="dataPath" jdbcType="VARCHAR" />
        <result column="interface_record" property="interfaceRecord" jdbcType="VARCHAR" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="duration" property="duration" jdbcType="BIGINT" />
        <result column="type" property="type" jdbcType="CHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.system.entity.SysDynamicLogDO" extends="BaseResultMap">
        <result column="params" property="params" jdbcType="LONGVARCHAR"/>
        <result column="exception" property="exception" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, request_id, operator, operation_action, execution_target, operator_ip, operator_time,
        operator_status, data_size, data_size_type, application_name, data_it, data_path,
        interface_record, end_time, duration, type
    </sql>

    <sql id="Blob_Column_List">
        params, exception
    </sql>

    <update id="updateDynamicRspDataSize">
        update sys_dynamic_log
        set data_size = #{dataSize,jdbcType=BIGINT},
        data_size_type = #{dataSizeType,jdbcType=VARCHAR}
        where request_id = #{requestId,jdbcType=VARCHAR}
        and interface_record=#{interfaceRecord,jdbcType=VARCHAR}
    </update>

    <select id="getDynamicListByRequestId" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from sys_dynamic_log
        where request_id = #{requestId,jdbcType=VARCHAR}
    </select>

</mapper>
