<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.ISystemLogExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.bo.system.SystemLogBO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="CHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="msg_cd" property="msgCd" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="remote_addr" property="remoteAddr" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="request_uri" property="requestUri" jdbcType="VARCHAR"/>
        <result column="data_it" property="dataIt" jdbcType="VARCHAR"/>
        <result column="application_name" property="applicationName" jdbcType="VARCHAR"/>
        <result column="method" property="method" jdbcType="VARCHAR"/>
        <result column="request_id" property="logId" jdbcType="VARCHAR"/>
        <result column="rsp_data_size" property="rspDataSize" jdbcType="BIGINT"/>
        <result column="rsp_data_size_type" property="rspDataSizeType" jdbcType="VARCHAR"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="workspace_name" property="workspaceName" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.system.bo.system.SystemLogBO" extends="BaseResultMap">
        <result column="params" property="params" jdbcType="LONGVARCHAR"/>
        <result column="exception" property="exception" jdbcType="LONGVARCHAR"/>
        <collection property="dynamicLogs" column="request_id"
                    ofType="com.cmpay.hacp.system.log.bo.SysDynamicLogBO"
                    select="getDynamicLogs">
        </collection>
    </resultMap>

    <resultMap id="dynamicLogs" type="com.cmpay.hacp.system.log.bo.SysDynamicLogBO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="request_id" property="logId" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="operation_action" property="operationAction" jdbcType="VARCHAR"/>
        <result column="execution_target" property="executionTarget" jdbcType="VARCHAR"/>
        <result column="operator_ip" property="operatorIp" jdbcType="VARCHAR"/>
        <result column="operator_time" property="operatorTime" jdbcType="TIMESTAMP"/>
        <result column="operator_status" property="operatorStatus" jdbcType="VARCHAR"/>
        <result column="data_size" property="dataSize" jdbcType="BIGINT"/>
        <result column="data_size_type" property="dataSizeType" jdbcType="VARCHAR"/>
        <result column="application_name" property="applicationName" jdbcType="VARCHAR"/>
        <result column="data_it" property="dataIt" jdbcType="VARCHAR"/>
        <result column="data_path" property="dataPath" jdbcType="VARCHAR"/>
        <result column="interface_record" property="interfaceRecord" jdbcType="VARCHAR"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="CHAR"/>
        <result column="params" property="params" jdbcType="LONGVARCHAR"/>
        <result column="exception" property="exception" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <select id="getSystemLogs" parameterType="com.cmpay.hacp.system.bo.system.SystemLogBO" resultMap="ResultMapWithBLOBs">
        SELECT
        t1.id,
        t1.type,
        t1.title,
        t1.user_id,
        t1.user_name,
        t1.msg_cd,
        t1.mobile,
        t1.create_by,
        t1.create_date,
        t1.remote_addr,
        t1.user_agent,
        t1.request_uri,
        t1.data_it,
        t1.application_name,
        t1.method,
        t1.request_id,
        t1.rsp_data_size,
        t1.rsp_data_size_type,
        t1.duration,
        t1.end_time,
        t1.params,
        t1.exception,
        t1.tenant_id,
        t1.workspace_id,
        t3.tenant_name,
        t4.workspace_name
        FROM
        sys_log t1
        LEFT JOIN tenant t3 ON t1.tenant_id = t3.tenant_id
        LEFT JOIN workspace t4 ON t1.workspace_id = t4.workspace_id
        <where>
            <if test="id != null and id !=''">
                and t1.id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="type != null and type !=''">
                and t1.type = #{type,jdbcType=CHAR}
            </if>
            <if test="title != null and title !=''">
                and t1.title = #{title,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and userId !=''">
                and t1.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null and userName !=''">
                and t1.user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="msgCd != null and msgCd !=''">
                and t1.msg_cd = #{msgCd,jdbcType=VARCHAR}
            </if>
            <if test="mobile != null and mobile !=''">
                and t1.mobile = #{mobile,jdbcType=VARCHAR}
            </if>
            <if test="createBy != null and createBy !=''">
                and t1.create_by = #{createBy,jdbcType=VARCHAR}
            </if>
            <if test="beginDate != null and endDate != null">
                and (t1.create_date between #{beginDate,jdbcType=TIMESTAMP} and #{endDate,jdbcType=TIMESTAMP})
            </if>
            <if test="remoteAddr != null and remoteAddr != ''">
                and t1.remote_addr = #{remoteAddr,jdbcType=VARCHAR}
            </if>
            <if test="userAgent != null and userAgent != ''">
                and t1.user_agent = #{userAgent,jdbcType=VARCHAR}
            </if>
            <if test="requestUri != null and requestUri !=''">
                and t1.request_uri = #{requestUri,jdbcType=VARCHAR}
            </if>
            <if test="dataIt != null and dataIt !=''">
                and t1.data_it = #{dataIt,jdbcType=VARCHAR}
            </if>
            <if test="applicationName != null and applicationName !=''">
                and t1.application_name = #{applicationName,jdbcType=VARCHAR}
            </if>
            <if test="method != null and method !=''">
                and t1.method = #{method,jdbcType=VARCHAR}
            </if>
            <if test="params != null and params !=''">
                and t1.params like concat('%',#{params,jdbcType=LONGVARCHAR},'%')
            </if>
            <if test="exception != null and exception !='' ">
                and t1.exception = #{exception,jdbcType=LONGVARCHAR}
            </if>
            <if test="logId != null and logId !=''">
                and t1.request_id = #{logId,jdbcType=VARCHAR}
            </if>
            <if test="rspDataSize != null">
                and t1.rsp_data_size = #{rspDataSize,jdbcType=BIGINT}
            </if>
            <if test="rspDataSizeType != null and rspDataSizeType !=''">
                and t1.rsp_data_size_type = #{rspDataSizeType,jdbcType=VARCHAR}
            </if>
            <if test="duration != null">
                and t1.duration = #{duration,jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                and t1.end_time = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="tenantId != null and tenantId !='' ">
                and t1.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null and workspaceId !='' ">
                and t1.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
        </where>
        order by t1.create_date desc
    </select>

    <select id="getSystemLogInfo" resultMap="ResultMapWithBLOBs">
        SELECT
            t1.id,
            t1.type,
            t1.title,
            t1.user_id,
            t1.user_name,
            t1.msg_cd,
            t1.mobile,
            t1.create_by,
            t1.create_date,
            t1.remote_addr,
            t1.user_agent,
            t1.request_uri,
            t1.data_it,
            t1.application_name,
            t1.method,
            t1.request_id,
            t1.rsp_data_size,
            t1.rsp_data_size_type,
            t1.duration,
            t1.end_time,
            t1.params,
            t1.exception,
            t1.tenant_id,
            t1.workspace_id,
            t3.tenant_name,
            t4.workspace_name
        FROM
            sys_log t1
                LEFT JOIN tenant t3 ON t1.tenant_id = t3.tenant_id
                LEFT JOIN workspace t4 ON t1.workspace_id = t4.workspace_id
        where t1.id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="getDynamicLogs" resultMap="dynamicLogs">
        select id,
               request_id,
               operator,
               operation_action,
               execution_target,
               operator_ip,
               operator_time,
               operator_status,
               data_size,
               data_size_type,
               application_name,
               data_it,
               data_path,
               interface_record,
               end_time,
               duration,
               type
        from sys_dynamic_log
        where request_id = #{requestId,jdbcType=VARCHAR}
        order by operator_time asc
    </select>

    <delete id="deleteByIds">
        delete from sys_log where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
