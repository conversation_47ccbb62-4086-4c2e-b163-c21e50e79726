<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.ISystemLogDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.SystemLogDO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="CHAR" />
        <result column="title" property="title" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="msg_cd" property="msgCd" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="create_by" property="createBy" jdbcType="VARCHAR" />
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
        <result column="remote_addr" property="remoteAddr" jdbcType="VARCHAR" />
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR" />
        <result column="request_uri" property="requestUri" jdbcType="VARCHAR" />
        <result column="application_name" property="applicationName" jdbcType="VARCHAR" />
        <result column="data_it" property="dataIt" jdbcType="VARCHAR" />
        <result column="method" property="method" jdbcType="VARCHAR" />
        <result column="request_id" property="requestId" jdbcType="VARCHAR" />
        <result column="rsp_data_size" property="rspDataSize" jdbcType="BIGINT" />
        <result column="rsp_data_size_type" property="rspDataSizeType" jdbcType="VARCHAR" />
        <result column="duration" property="duration" jdbcType="BIGINT" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.system.entity.SystemLogDO" extends="BaseResultMap" >
        <result column="params" property="params" jdbcType="LONGVARCHAR" />
        <result column="exception" property="exception" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, type, title, user_id, user_name, msg_cd, mobile, create_by, create_date, remote_addr, 
        user_agent, request_uri, application_name, data_it, method, request_id, rsp_data_size, 
        rsp_data_size_type, duration, end_time, tenant_id, workspace_id
    </sql>

    <sql id="Blob_Column_List" >
        params, exception
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from sys_log
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from sys_log
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.system.entity.SystemLogDO" >
        insert into sys_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="title != null" >
                title,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="userName != null" >
                user_name,
            </if>
            <if test="msgCd != null" >
                msg_cd,
            </if>
            <if test="mobile != null" >
                mobile,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createDate != null" >
                create_date,
            </if>
            <if test="remoteAddr != null" >
                remote_addr,
            </if>
            <if test="userAgent != null" >
                user_agent,
            </if>
            <if test="requestUri != null" >
                request_uri,
            </if>
            <if test="applicationName != null" >
                application_name,
            </if>
            <if test="dataIt != null" >
                data_it,
            </if>
            <if test="method != null" >
                method,
            </if>
            <if test="requestId != null" >
                request_id,
            </if>
            <if test="rspDataSize != null" >
                rsp_data_size,
            </if>
            <if test="rspDataSizeType != null" >
                rsp_data_size_type,
            </if>
            <if test="duration != null" >
                duration,
            </if>
            <if test="endTime != null" >
                end_time,
            </if>
            <if test="tenantId != null" >
                tenant_id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="params != null" >
                params,
            </if>
            <if test="exception != null" >
                exception,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=CHAR},
            </if>
            <if test="title != null" >
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="msgCd != null" >
                #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null" >
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null" >
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="remoteAddr != null" >
                #{remoteAddr,jdbcType=VARCHAR},
            </if>
            <if test="userAgent != null" >
                #{userAgent,jdbcType=VARCHAR},
            </if>
            <if test="requestUri != null" >
                #{requestUri,jdbcType=VARCHAR},
            </if>
            <if test="applicationName != null" >
                #{applicationName,jdbcType=VARCHAR},
            </if>
            <if test="dataIt != null" >
                #{dataIt,jdbcType=VARCHAR},
            </if>
            <if test="method != null" >
                #{method,jdbcType=VARCHAR},
            </if>
            <if test="requestId != null" >
                #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="rspDataSize != null" >
                #{rspDataSize,jdbcType=BIGINT},
            </if>
            <if test="rspDataSizeType != null" >
                #{rspDataSizeType,jdbcType=VARCHAR},
            </if>
            <if test="duration != null" >
                #{duration,jdbcType=BIGINT},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="params != null" >
                #{params,jdbcType=LONGVARCHAR},
            </if>
            <if test="exception != null" >
                #{exception,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.system.entity.SystemLogDO" >
        update sys_log
        <set >
            <if test="type != null" >
                type = #{type,jdbcType=CHAR},
            </if>
            <if test="title != null" >
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="msgCd != null" >
                msg_cd = #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null" >
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="remoteAddr != null" >
                remote_addr = #{remoteAddr,jdbcType=VARCHAR},
            </if>
            <if test="userAgent != null" >
                user_agent = #{userAgent,jdbcType=VARCHAR},
            </if>
            <if test="requestUri != null" >
                request_uri = #{requestUri,jdbcType=VARCHAR},
            </if>
            <if test="applicationName != null" >
                application_name = #{applicationName,jdbcType=VARCHAR},
            </if>
            <if test="dataIt != null" >
                data_it = #{dataIt,jdbcType=VARCHAR},
            </if>
            <if test="method != null" >
                method = #{method,jdbcType=VARCHAR},
            </if>
            <if test="requestId != null" >
                request_id = #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="rspDataSize != null" >
                rsp_data_size = #{rspDataSize,jdbcType=BIGINT},
            </if>
            <if test="rspDataSizeType != null" >
                rsp_data_size_type = #{rspDataSizeType,jdbcType=VARCHAR},
            </if>
            <if test="duration != null" >
                duration = #{duration,jdbcType=BIGINT},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tenantId != null" >
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="params != null" >
                params = #{params,jdbcType=LONGVARCHAR},
            </if>
            <if test="exception != null" >
                exception = #{exception,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.system.entity.SystemLogDO" >
        update sys_log
        set type = #{type,jdbcType=CHAR},
            title = #{title,jdbcType=VARCHAR},
            user_id = #{userId,jdbcType=VARCHAR},
            user_name = #{userName,jdbcType=VARCHAR},
            msg_cd = #{msgCd,jdbcType=VARCHAR},
            mobile = #{mobile,jdbcType=VARCHAR},
            create_by = #{createBy,jdbcType=VARCHAR},
            create_date = #{createDate,jdbcType=TIMESTAMP},
            remote_addr = #{remoteAddr,jdbcType=VARCHAR},
            user_agent = #{userAgent,jdbcType=VARCHAR},
            request_uri = #{requestUri,jdbcType=VARCHAR},
            application_name = #{applicationName,jdbcType=VARCHAR},
            data_it = #{dataIt,jdbcType=VARCHAR},
            method = #{method,jdbcType=VARCHAR},
            request_id = #{requestId,jdbcType=VARCHAR},
            rsp_data_size = #{rspDataSize,jdbcType=BIGINT},
            rsp_data_size_type = #{rspDataSizeType,jdbcType=VARCHAR},
            duration = #{duration,jdbcType=BIGINT},
            end_time = #{endTime,jdbcType=TIMESTAMP},
            tenant_id = #{tenantId,jdbcType=VARCHAR},
            workspace_id = #{workspaceId,jdbcType=VARCHAR},
            params = #{params,jdbcType=LONGVARCHAR},
            exception = #{exception,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.system.entity.SystemLogDO" >
        select 
        <include refid="Base_Column_List" />
        from sys_log
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="type != null" >
                and type = #{type,jdbcType=CHAR}
            </if>
            <if test="title != null" >
                and title = #{title,jdbcType=VARCHAR}
            </if>
            <if test="userId != null" >
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="msgCd != null" >
                and msg_cd = #{msgCd,jdbcType=VARCHAR}
            </if>
            <if test="mobile != null" >
                and mobile = #{mobile,jdbcType=VARCHAR}
            </if>
            <if test="createBy != null" >
                and create_by = #{createBy,jdbcType=VARCHAR}
            </if>
            <if test="createDate != null" >
                and create_date = #{createDate,jdbcType=TIMESTAMP}
            </if>
            <if test="remoteAddr != null" >
                and remote_addr = #{remoteAddr,jdbcType=VARCHAR}
            </if>
            <if test="userAgent != null" >
                and user_agent = #{userAgent,jdbcType=VARCHAR}
            </if>
            <if test="requestUri != null" >
                and request_uri = #{requestUri,jdbcType=VARCHAR}
            </if>
            <if test="applicationName != null" >
                and application_name = #{applicationName,jdbcType=VARCHAR}
            </if>
            <if test="dataIt != null" >
                and data_it = #{dataIt,jdbcType=VARCHAR}
            </if>
            <if test="method != null" >
                and method = #{method,jdbcType=VARCHAR}
            </if>
            <if test="requestId != null" >
                and request_id = #{requestId,jdbcType=VARCHAR}
            </if>
            <if test="rspDataSize != null" >
                and rsp_data_size = #{rspDataSize,jdbcType=BIGINT}
            </if>
            <if test="rspDataSizeType != null" >
                and rsp_data_size_type = #{rspDataSizeType,jdbcType=VARCHAR}
            </if>
            <if test="duration != null" >
                and duration = #{duration,jdbcType=BIGINT}
            </if>
            <if test="endTime != null" >
                and end_time = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="tenantId != null" >
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="params != null" >
                and params = #{params,jdbcType=LONGVARCHAR}
            </if>
            <if test="exception != null" >
                and exception = #{exception,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>