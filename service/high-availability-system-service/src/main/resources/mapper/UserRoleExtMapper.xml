<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.IUserRoleExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.UserRoleDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
    </resultMap>

    <resultMap id="BaseResultExtMap" type="com.cmpay.hacp.system.entity.RoleDO">
        <id column="ROLE_ID" property="roleId" jdbcType="BIGINT"/>
        <result column="ROLE_NAME" property="roleName" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="CREATE_USER_ID" property="createUserId" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="OWNER_APP_ID" property="ownerAppId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        user_id,
        role_id
    </sql>

    <insert id="batchInsertUserRole">
        insert into sys_user_role (id, user_id, role_id) values
        <foreach item="roleId" collection="roleIdList" separator=",">
            (CONCAT(CONCAT(#{userId}, '-'), #{roleId}), #{userId}, #{roleId})
        </foreach>
    </insert>

    <delete id="deleteByUserId" parameterType="java.lang.String">
        delete
        from sys_user_role
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByRoleId" parameterType="java.lang.Long">
        delete
        from sys_user_role
        where role_id = #{roleId,jdbcType=BIGINT}
    </delete>

    <select id="getUsersByRoleId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select u.user_id,
               u.user_name,
               u.full_name,
               u.password,
               u.salt,
               u.dept_id,
               u.duty_id,
               u.email,
               u.mobile,
               u.weixin,
               u.status,
               u.create_user_id,
               u.create_time,
               u.modify_time,
               u.last_login_time,
               u.has_role,
               u.cst_user_id,
               u.app_id,
               u.pwd_modify_time
        from sys_user as u
                 left join sys_user_role as r on r.role_id = #{roleId,jdbcType=BIGINT}
        where r.user_id = u.user_id
    </select>

    <select id="getRolesByUserId" parameterType="java.lang.String" resultMap="BaseResultExtMap">
        select u.role_id,
               u.role_name,
               u.remark,
               u.status,
               u.dept_id,
               u.create_user_id,
               u.create_time,
               u.modify_time,
               u.owner_app_id
        from sys_role as u
                 left join sys_user_role as r on r.user_id = #{userId,jdbcType=VARCHAR}
        where r.role_id = u.role_id
    </select>
</mapper>
